# 移动端响应式设计系统使用指南

## 概述

本项目已经集成了完整的移动端响应式设计系统，支持手机、平板、桌面等多种设备的自适应布局。系统基于uni-app框架，提供了丰富的响应式组件和工具类。

## 设备支持

### 设备类型分类
- **small-phone**: 小屏手机 (< 375px)
- **phone**: 标准手机 (375px - 414px)
- **large-phone**: 大屏手机 (414px - 768px)
- **tablet**: 平板设备 (768px - 1024px)
- **desktop**: 桌面设备 (> 1024px)

### 屏幕尺寸分类
- **xs**: 超小屏 (< 375px)
- **sm**: 小屏 (375px - 414px)
- **md**: 中屏 (414px - 768px)
- **lg**: 大屏 (768px - 1024px)
- **xl**: 超大屏 (> 1024px)

## 核心组件

### 1. ResponsiveLayout 响应式布局容器

```vue
<template>
  <ResponsiveLayout 
    :padding="'base'" 
    :container="true" 
    backgroundColor="#f5f5f5"
  >
    <!-- 页面内容 -->
  </ResponsiveLayout>
</template>
```

**属性说明:**
- `padding`: 内边距配置 ('small', 'base', 'large' 或对象)
- `container`: 是否启用容器最大宽度限制
- `backgroundColor`: 背景色
- `customClass`: 自定义类名

### 2. ResponsiveGrid 响应式网格

```vue
<template>
  <ResponsiveGrid 
    :cols="{ xs: 1, sm: 2, md: 3, lg: 4, xl: 6 }" 
    gap="24rpx"
  >
    <view class="grid-item">项目1</view>
    <view class="grid-item">项目2</view>
    <view class="grid-item">项目3</view>
  </ResponsiveGrid>
</template>
```

**属性说明:**
- `cols`: 列数配置，可以是数字或对象
- `gap`: 网格间距
- `align`: 垂直对齐方式
- `justify`: 水平对齐方式

### 3. ResponsiveCard 响应式卡片

```vue
<template>
  <ResponsiveCard 
    title="卡片标题" 
    subtitle="副标题" 
    :shadow="true"
    :clickable="true"
    @click="handleClick"
  >
    <template #header>
      <!-- 自定义头部内容 -->
    </template>
    
    <!-- 卡片主体内容 -->
    <view>卡片内容</view>
    
    <template #footer>
      <!-- 自定义底部内容 -->
    </template>
  </ResponsiveCard>
</template>
```

## 全局混入方法

所有页面和组件都可以使用以下响应式方法：

### 设备检测
```javascript
// 检测设备类型
this.$isPhone        // 是否为手机
this.$isTablet       // 是否为平板
this.$isDesktop      // 是否为桌面端
this.$isSmallScreen  // 是否为小屏设备
this.$isLargeScreen  // 是否为大屏设备

// 获取设备信息
this.$device.type        // 设备类型
this.$device.screenSize  // 屏幕尺寸分类
this.$device.systemInfo  // 系统信息
```

### 尺寸转换
```javascript
// rpx与px转换
this.$rpxToPx(750)  // 转换为px
this.$pxToRpx(375)  // 转换为rpx

// 获取适配后的尺寸
this.$getAdaptiveSize(100, 'width')
```

### 响应式配置
```javascript
// 获取网格列数
this.$getGridColumns(2)  // 默认2列

// 获取卡片间距
this.$getCardSpacing()

// 获取字体大小
this.$getFontSize('base')  // 'small', 'base', 'large', 'xl'

// 获取内边距
this.$getPadding('base')   // 'small', 'base', 'large'
```

### 样式获取
```javascript
// 根据设备类型获取样式
this.$getDeviceStyle({
  'small-phone': '16rpx',
  'phone': '20rpx',
  'tablet': '32rpx',
  'default': '24rpx'
})

// 根据屏幕尺寸获取样式
this.$getScreenStyle({
  'xs': '16rpx',
  'sm': '20rpx',
  'md': '24rpx',
  'lg': '32rpx',
  'xl': '40rpx',
  'default': '24rpx'
})
```

## CSS工具类

### 响应式显示/隐藏
```css
.d-xs-none      /* 在xs屏幕隐藏 */
.d-sm-block     /* 在sm屏幕显示为block */
.d-md-flex      /* 在md屏幕显示为flex */
```

### 响应式文本对齐
```css
.text-xs-center   /* 在xs屏幕居中对齐 */
.text-md-left     /* 在md屏幕左对齐 */
.text-lg-right    /* 在lg屏幕右对齐 */
```

### 响应式间距
```css
.p-xs-small      /* 在xs屏幕使用小内边距 */
.m-md-base       /* 在md屏幕使用基础外边距 */
.px-lg-large     /* 在lg屏幕使用大水平内边距 */
```

### 响应式字体
```css
.text-xs-small   /* 在xs屏幕使用小字体 */
.text-md-base    /* 在md屏幕使用基础字体 */
.text-lg-large   /* 在lg屏幕使用大字体 */
```

## 实际使用示例

### 1. 仪表板页面适配

```vue
<template>
  <ResponsiveLayout class="dashboard" :padding="'base'">
    <!-- 统计卡片 -->
    <ResponsiveCard title="人员情况" :subtitle="currentDate">
      <ResponsiveGrid :cols="personGridCols">
        <view class="stat-item">总数: 208</view>
        <view class="stat-item">在岗: 186</view>
        <view class="stat-item">请假: 24</view>
        <view class="stat-item">异常: 6</view>
      </ResponsiveGrid>
    </ResponsiveCard>
    
    <!-- 图表区域 -->
    <ResponsiveCard title="趋势图表">
      <view class="chart-container" :style="{ height: chartHeight }">
        <!-- 图表组件 -->
      </view>
    </ResponsiveCard>
  </ResponsiveLayout>
</template>

<script>
export default {
  computed: {
    personGridCols() {
      return this.$getDeviceStyle({
        'small-phone': { xs: 2, sm: 2 },
        'phone': { xs: 2, sm: 2, md: 4 },
        'tablet': { xs: 2, sm: 2, md: 4, lg: 4 },
        'default': { xs: 2, sm: 2, md: 4 }
      })
    },
    
    chartHeight() {
      return this.$getDeviceStyle({
        'small-phone': '200px',
        'phone': '230px',
        'tablet': '280px',
        'default': '230px'
      })
    }
  }
}
</script>
```

### 2. 列表页面适配

```vue
<template>
  <ResponsiveLayout>
    <ResponsiveGrid :cols="listCols" gap="16rpx">
      <ResponsiveCard 
        v-for="item in list" 
        :key="item.id"
        :title="item.title"
        :clickable="true"
        @click="handleItemClick(item)"
      >
        <view class="item-content">{{ item.content }}</view>
      </ResponsiveCard>
    </ResponsiveGrid>
  </ResponsiveLayout>
</template>

<script>
export default {
  computed: {
    listCols() {
      return this.$getDeviceStyle({
        'small-phone': 1,
        'phone': 1,
        'large-phone': 2,
        'tablet': 3,
        'desktop': 4,
        'default': 1
      })
    }
  }
}
</script>
```

## 最佳实践

### 1. 设计原则
- **移动优先**: 先设计小屏幕，再适配大屏幕
- **渐进增强**: 基础功能在所有设备上可用，高级功能在大屏设备上增强
- **内容优先**: 确保核心内容在所有设备上都能良好展示

### 2. 性能优化
- 使用CSS媒体查询而不是JavaScript检测
- 避免在小屏设备上加载大图片
- 合理使用响应式组件，避免过度嵌套

### 3. 测试建议
- 在不同尺寸的设备上测试
- 使用浏览器开发者工具模拟不同设备
- 关注横屏和竖屏切换

## 常见问题

### Q: 如何自定义断点？
A: 修改 `static/scss/responsive.scss` 中的 `$breakpoints` 变量。

### Q: 如何添加新的响应式组件？
A: 参考现有组件结构，使用设备检测工具和响应式混入。

### Q: 如何处理图片的响应式？
A: 使用 `.responsive-image` 类或 `$getImageSize()` 方法。

### Q: 如何在不同设备上显示不同内容？
A: 使用 `v-if` 配合设备检测方法，如 `v-if="$isTablet"`。

## 更新日志

- **v1.0.0**: 初始版本，包含基础响应式组件和工具类
- 支持手机、平板、桌面设备自适应
- 提供完整的响应式设计系统
