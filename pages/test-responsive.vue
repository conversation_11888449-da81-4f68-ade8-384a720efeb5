<template>
  <view class="test-page responsive-container">
    <view class="responsive-card">
      <view class="responsive-card-header">
        <view class="responsive-card-title">响应式测试页面</view>
        <view class="responsive-card-subtitle">当前设备类型: {{ deviceType }}</view>
      </view>
      
      <view class="responsive-card-body">
        <!-- 设备信息展示 -->
        <view class="info-section responsive-m-base">
          <text class="responsive-text-lg">设备信息:</text>
          <view class="info-item">
            <text class="responsive-text-base">屏幕宽度: {{ deviceInfo.screenWidth }}px</text>
          </view>
          <view class="info-item">
            <text class="responsive-text-base">屏幕高度: {{ deviceInfo.screenHeight }}px</text>
          </view>
          <view class="info-item">
            <text class="responsive-text-base">设备类型: {{ deviceType }}</text>
          </view>
        </view>
        
        <!-- 响应式网格测试 -->
        <view class="grid-section responsive-m-base">
          <text class="responsive-text-lg">响应式网格测试:</text>
          <view class="responsive-grid responsive-m-sm">
            <view class="responsive-grid-item grid-col-2">
              <view class="test-card responsive-card">
                <view class="responsive-card-body">
                  <text class="responsive-text-base">卡片1</text>
                </view>
              </view>
            </view>
            <view class="responsive-grid-item grid-col-2">
              <view class="test-card responsive-card">
                <view class="responsive-card-body">
                  <text class="responsive-text-base">卡片2</text>
                </view>
              </view>
            </view>
            <view class="responsive-grid-item grid-col-2">
              <view class="test-card responsive-card">
                <view class="responsive-card-body">
                  <text class="responsive-text-base">卡片3</text>
                </view>
              </view>
            </view>
            <view class="responsive-grid-item grid-col-2">
              <view class="test-card responsive-card">
                <view class="responsive-card-body">
                  <text class="responsive-text-base">卡片4</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 字体大小测试 -->
        <view class="font-section responsive-m-base">
          <text class="responsive-text-lg">字体大小测试:</text>
          <view class="font-item">
            <text class="responsive-text-xs">超小字体 (xs)</text>
          </view>
          <view class="font-item">
            <text class="responsive-text-sm">小字体 (sm)</text>
          </view>
          <view class="font-item">
            <text class="responsive-text-base">基础字体 (base)</text>
          </view>
          <view class="font-item">
            <text class="responsive-text-lg">大字体 (lg)</text>
          </view>
          <view class="font-item">
            <text class="responsive-text-xl">超大字体 (xl)</text>
          </view>
          <view class="font-item">
            <text class="responsive-text-2xl">特大字体 (2xl)</text>
          </view>
        </view>
        
        <!-- 间距测试 -->
        <view class="spacing-section responsive-m-base">
          <text class="responsive-text-lg">间距测试:</text>
          <view class="spacing-item responsive-p-xs" style="background: #f0f0f0; margin-bottom: 10rpx;">
            <text class="responsive-text-sm">超小内边距 (xs)</text>
          </view>
          <view class="spacing-item responsive-p-sm" style="background: #f0f0f0; margin-bottom: 10rpx;">
            <text class="responsive-text-sm">小内边距 (sm)</text>
          </view>
          <view class="spacing-item responsive-p-base" style="background: #f0f0f0; margin-bottom: 10rpx;">
            <text class="responsive-text-sm">基础内边距 (base)</text>
          </view>
          <view class="spacing-item responsive-p-lg" style="background: #f0f0f0; margin-bottom: 10rpx;">
            <text class="responsive-text-sm">大内边距 (lg)</text>
          </view>
        </view>
        
        <!-- 按钮测试 -->
        <view class="button-section responsive-m-base">
          <text class="responsive-text-lg">按钮测试:</text>
          <button class="responsive-button" style="background: #007aff; color: white; margin-top: 20rpx;">
            <text class="responsive-text-base">响应式按钮</text>
          </button>
        </view>
        
        <!-- 显示/隐藏测试 -->
        <view class="visibility-section responsive-m-base">
          <text class="responsive-text-lg">显示/隐藏测试:</text>
          <view class="show-mobile" style="background: #4cd964; padding: 20rpx; margin: 10rpx 0;">
            <text class="responsive-text-sm" style="color: white;">仅在手机上显示</text>
          </view>
          <view class="show-tablet" style="background: #f0ad4e; padding: 20rpx; margin: 10rpx 0;">
            <text class="responsive-text-sm" style="color: white;">仅在平板上显示</text>
          </view>
          <view class="show-desktop" style="background: #dd524d; padding: 20rpx; margin: 10rpx 0;">
            <text class="responsive-text-sm" style="color: white;">仅在桌面上显示</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getDeviceInfo, getDeviceType } from '@/utils/responsive'

export default {
  name: 'TestResponsive',
  data() {
    return {
      deviceInfo: {},
      deviceType: ''
    }
  },
  
  created() {
    this.loadDeviceInfo()
  },
  
  methods: {
    loadDeviceInfo() {
      this.deviceInfo = getDeviceInfo()
      this.deviceType = getDeviceType()
      console.log('设备信息:', this.deviceInfo)
      console.log('设备类型:', this.deviceType)
    }
  }
}
</script>

<style lang="scss" scoped>
.test-page {
  min-height: 100vh;
  background: #f5f5f5;
  padding-top: 40rpx;
  padding-bottom: 40rpx;
}

.info-section,
.grid-section,
.font-section,
.spacing-section,
.button-section,
.visibility-section {
  margin-bottom: 40rpx;
}

.info-item,
.font-item {
  margin: 10rpx 0;
}

.test-card {
  background: #e3f2fd;
  margin-bottom: 20rpx;
}

.spacing-item {
  border-radius: 8rpx;
}
</style>
