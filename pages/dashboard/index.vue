<template>
  <view class="viewport dashboard-index">
    <view class="top-box">
      <!-- 头部导航栏 -->
      <view class="header-nav">
        <view class="logo">
          <image class="image" mode="heightFix" src="@/static/images/logo.png" />
        </view>
        <!-- 右上角菜单按钮 -->
        <view class="menu-button" @click="toggleMenu">
          <image class="menu-icon" mode="aspectFit" src="@/static/images/icons/list_tigger.png" />
        </view>
      </view>

      <!-- 项目选择器 -->
      <view class="project-selector-wrapper">
        <picker
          :range="projects"
          range-key="name"
          :value="selectedProjectIndex"
          @change="onProjectChange"
          :disabled="projectsLoading || projects.length === 0"
        >
          <view class="project-selector" :class="{
            'loading': projectsLoading,
            'disabled': projectsLoading || projects.length === 0
          }">
              <view class="selected-project">
                <image class="icon" mode="aspectFit" src="@/static/images/icons/floder.png" />
                <text class="text">
                  {{ getProjectDisplayText() }}
                </text>
              </view>
              <view class="selector-actions">
                <!-- 清除按钮 - 只在有选中项目时显示 -->
                <view
                  v-if="selectedProject && !projectsLoading"
                  class="clear-button"
                  :class="{ 'first-show': clearButtonFirstShow }"
                  @click.stop="clearProjectSelection"
                  @longpress="showClearTip"
                >
                  <image class="clear-icon" mode="aspectFit" src="/static/images/icons/close.png" />
                  <!-- 清除提示文字 -->
                  <view class="clear-tip">清除</view>
                </view>
                <!-- 下拉箭头 -->
                <image
                  class="dropdown-icon"
                  mode="aspectFit"
                  src="@/static/images/icons/list_tigger.png"
                  :class="{ 'rotate': projectsLoading }"
                />
              </view>
          </view>
        </picker>
      </view>
    </view>

    <!-- 下拉菜单 -->
    <view v-if="showMenu" class="dropdown-menu">
      <view class="menu-list">
        <view
          v-for="(item, index) in menuItems"
          :key="index"
          class="menu-item"
          @click="navigateToPage(item)"
        >
          <view class="icon-wrapper">
            <image
              class="menu-item-icon"
              mode="aspectFit"
              :src="item.icon"
              @error="onImageError"
              @load="onImageLoad"
            />
            <!-- 备用图标显示 -->
            <text v-if="!item.iconLoaded" class="icon-text">{{ getIconText(item.id) }}</text>
          </view>
          <text class="menu-text">{{ item.name }}</text>
        </view>
      </view>
    </view>

    <!-- 菜单遮罩层，点击关闭菜单 -->
    <view v-if="showMenu" class="menu-mask" @click="closeMenu"></view>

    <view class="box person">
      <view class="title">
        <view class="content">
          <image class="icon" mode="aspectFit" src="@/static/images/icons/person.png" />
          <text class="text">人员情况</text>
        </view>
        <view class="extra">
          <text class="date">{{ currentDate }}</text>
        </view>
      </view>
      <view class="body">
        <!-- 加载状态提示 -->
        <view v-if="userCountLoading" class="loading-container">
          <text class="loading-text">加载中...</text>
        </view>

        <!-- 人员数据展示 -->
        <uni-row v-else :gutter="12" class="card-row">
          <uni-col :span="9">
            <view class="item primary">
              <view class="content">
                <view class="num">{{ userCountData.totalCount }}</view>
                <view class="label">人员总数</view>
              </view>
            </view>
          </uni-col>
          <uni-col :span="5">
            <view class="item blue">
              <image class="bg-icon" mode="aspectFit" src="@/static/images/icons/user_group.png" />
              <view class="content">
                <view class="num">{{ userCountData.onDutyCount }}</view>
                <view class="label">在岗</view>
              </view>
            </view>
          </uni-col>
          <uni-col :span="5">
            <view class="item green">
              <image class="bg-icon" mode="aspectFit" src="@/static/images/icons/calendar.png" />
              <view class="content">
                <view class="num">{{ userCountData.leaveCount }}</view>
                <view class="label">请假</view>
              </view>
            </view>
          </uni-col>
          <uni-col :span="5">
            <view class="item pink">
              <image class="bg-icon" mode="aspectFit" src="@/static/images/icons/warning.png" />
              <view class="content">
                <view class="num">{{ userCountData.abnormalCount }}</view>
                <view class="label">异常</view>
              </view>
            </view>
          </uni-col>
        </uni-row>
      </view>
    </view>
    
    <view class="box ticket">
      <view class="title">
        <view class="content">
          <image class="icon" mode="aspectFit" src="@/static/images/icons/file.png" />
          <text class="text">工单情况</text>
        </view>
        <view class="extra">
          <text class="date">{{ currentDate }}</text>
        </view>
      </view>
      <view class="body">
        <uni-row :gutter="12" class="card-row">
          <uni-col :span="10">
            <view class="item primary">
              <view class="content">
                <view class="num">1051</view>
                <view class="label">当月工单数</view>
              </view>
            </view>
          </uni-col>
          <uni-col :span="7">
            <view class="item green">
              <image class="bg-icon" mode="aspectFit" src="@/static/images/icons/todo_checked.png" />
              <view class="content">
                <view class="num">24</view>
                <view class="label">办结</view>
              </view>
            </view>
          </uni-col>
          <uni-col :span="7">
            <view class="item blue">
              <image class="bg-icon" mode="aspectFit" src="@/static/images/icons/clock.png" />
              <view class="content">
                <view class="num">1018</view>
                <view class="label">进行中</view>
              </view>
            </view>
          </uni-col>
        </uni-row>

        <view class="sub-title">
          <view class="content">
            <text>工单趋势</text>
          </view>
          <view class="extra">
            <view class="tab-list">
              <view class="item active">
                <text>本月</text>
              </view>
              <view class="item">
                <text>本年</text>
              </view>
            </view>
          </view>
        </view>
        <view class="chart-wrapper">
          <ve-line
            height="230px"
            :data="lineData"
            :grid="chartGrid"
            :settings="lineChartSettings"
            :legend-visible="false"
            :extend="lineChartExtra"
            :colors="['#006ED9']"
          />
        </view>
        
        <view class="sub-title">
          <view class="content">
            <text>工单分类</text>
          </view>
          <view class="extra">
            <view class="tab-list">
              <view class="item active">
                <text>本月</text>
              </view>
              <view class="item">
                <text>本年</text>
              </view>
            </view>
          </view>
        </view>
        <view class="chart-wrapper">
          <ve-ring
            height="230px"
            :data="ringData"
            :grid="chartGrid"
            :settings="ringChartSettings"
            :extend="ringChartExtra"
          />
        </view>

      </view>
    </view>

    <view class="box system">
      <view class="title">
        <view class="content">
          <image class="icon" mode="aspectFit" src="@/static/images/icons/system.png" />
          <text class="text">系统情况</text>
        </view>
        <view class="extra">
          <text class="date">{{ currentDate }}</text>
        </view>
      </view>
      <view class="body">
        <uni-row :gutter="12" class="card-row">
          <uni-col :span="10">
            <view class="item primary">
              <view class="content">
                <view class="num">45</view>
                <view class="label">系统数量</view>
              </view>
            </view>
          </uni-col>
          <uni-col :span="7">
            <view class="item green">
              <image class="bg-icon" mode="aspectFit" src="@/static/images/icons/shield.png" />
              <view class="content">
                <view class="num">45</view>
                <view class="label">正常</view>
              </view>
            </view>
          </uni-col>
          <uni-col :span="7">
            <view class="item pink">
              <image class="bg-icon" mode="aspectFit" src="@/static/images/icons/warning.png" />
              <view class="content">
                <view class="num">0</view>
                <view class="label">异常</view>
              </view>
            </view>
          </uni-col>
        </uni-row>

        <view class="sub-title">
          <view class="content">
            <text>异常趋势</text>
          </view>
          <view class="extra">
            <view class="tab-list">
              <view class="item active">
                <text>本月</text>
              </view>
              <view class="item">
                <text>本年</text>
              </view>
            </view>
          </view>
        </view>
        <view class="chart-wrapper responsive-chart">
          <ve-line
            :height="chartHeight"
            :data="lineData"
            :grid="chartGrid"
            :settings="lineChartSettings"
            :legend-visible="false"
            :extend="lineChartExtra"
            :colors="['#006ED9']"
          />
        </view>
      </view>
    </view>

    <view class="box server">
      <view class="title">
        <view class="content">
          <image class="icon" mode="aspectFit" src="@/static/images/icons/server.png" />
          <text class="text">人员情况</text>
        </view>
        <view class="extra">
          <text class="date">{{ currentDate }}</text>
        </view>
      </view>
      <view class="body">
        <uni-row :gutter="12" class="card-row">
          <uni-col :span="9">
            <view class="item primary">
              <view class="content">
                <view class="num">1051</view>
                <view class="label">数量</view>
              </view>
            </view>
          </uni-col>
          <uni-col :span="5">
            <view class="item blue">
              <image class="bg-icon" mode="aspectFit" src="@/static/images/icons/shield.png" />
              <view class="content">
                <view class="num">186</view>
                <view class="label">正常</view>
              </view>
            </view>
          </uni-col>
          <uni-col :span="5">
            <view class="item pink">
              <image class="bg-icon" mode="aspectFit" src="@/static/images/icons/warning.png" />
              <view class="content">
                <view class="num">6</view>
                <view class="label">异常</view>
              </view>
            </view>
          </uni-col>
          <uni-col :span="5">
            <view class="item gray">
              <image class="bg-icon" mode="aspectFit" src="@/static/images/icons/shutdown.png" />
              <view class="content">
                <view class="num">2</view>
                <view class="label">关机</view>
              </view>
            </view>
          </uni-col>
        </uni-row>

        <view class="sub-title">
          <view class="content">
            <text>异常情况</text>
          </view>
          <view class="extra">
            <view class="tab-list">
              <view class="item active">
                <text>本月</text>
              </view>
              <view class="item">
                <text>本年</text>
              </view>
            </view>
          </view>
        </view>
        <view class="chart-wrapper responsive-chart">
          <ve-line
            :height="chartHeight"
            :data="lineData"
            :grid="chartGrid"
            :settings="lineChartSettings"
            :legend-visible="false"
            :extend="lineChartExtra"
            :colors="['#006ED9']"
          />
        </view>
      </view>
    </view>

    <view class="box assets">
      <view class="title">
        <view class="content">
          <image class="icon" mode="aspectFit" src="@/static/images/icons/assets.png" />
          <text class="text">资金情况</text>
        </view>
        <view class="extra">
          <text class="date">{{ currentDate }}</text>
        </view>
      </view>
      <view class="body">
        <uni-row :gutter="12" class="card-row">
          <uni-col :span="9">
            <view class="item primary">
              <view class="content">
                <view class="num">1051</view>
                <view class="label">数量</view>
              </view>
            </view>
          </uni-col>
          <uni-col :span="5">
            <view class="item blue">
              <image class="bg-icon" mode="aspectFit" src="@/static/images/icons/shield.png" />
              <view class="content">
                <view class="num">186</view>
                <view class="label">正常</view>
              </view>
            </view>
          </uni-col>
          <uni-col :span="5">
            <view class="item pink">
              <image class="bg-icon" mode="aspectFit" src="@/static/images/icons/warning.png" />
              <view class="content">
                <view class="num">6</view>
                <view class="label">异常</view>
              </view>
            </view>
          </uni-col>
          <uni-col :span="5">
            <view class="item gray">
              <image class="bg-icon" mode="aspectFit" src="@/static/images/icons/shutdown.png" />
              <view class="content">
                <view class="num">2</view>
                <view class="label">关机</view>
              </view>
            </view>
          </uni-col>
        </uni-row>
      </view> 
    </view>

  </view>
</template>

<script>
  // echarts 图表组件使用文档参考：https://denaro-org.github.io/v-charts2/charts/line.html
  import Vue from 'vue'
  import VeLine from '@v-charts2/line'
  import VeRing from '@v-charts2/ring'
  import { getAllProjects, getProjectUserCountInfo } from '@/api/itsm/project'

  Vue.use(VeLine)
  Vue.use(VeRing)

  export default {
    name: 'DashboadIndex',

    components: {
      // VeLine
    },

    data() {
      return {
        // 当前日期，格式：YYYY-MM-DD
        currentDate: '',
        // 控制菜单显示状态
        showMenu: false,
        // 项目相关状态
        selectedProjectIndex: -1, // 当前选中的项目索引
        selectedProject: null, // 当前选中的项目对象
        projectsLoading: false, // 项目数据加载状态
        // 人员情况数据状态
        userCountData: {
          totalCount: 0,      // 人员总数
          onDutyCount: 0,     // 在岗人数
          leaveCount: 0,      // 请假人数
          abnormalCount: 0    // 异常人数
        },
        userCountLoading: false, // 人员数据加载状态
        // 清除按钮状态
        clearButtonFirstShow: false, // 是否首次显示清除按钮
        // 菜单项配置
        menuItems: [
          {
            id: 'person',
            name: '人员情况',
            icon: '/static/images/icons/user_group.png',
            path: '/pages/dashboard/person',
            iconLoaded: false
          },
          {
            id: 'ticket',
            name: '工单情况',
            icon: '/static/images/icons/file.png',
            path: '/pages/dashboard/ticket',
            iconLoaded: false
          },
          {
            id: 'system',
            name: '系统情况',
            icon: '/static/images/icons/system.png',
            path: '/pages/dashboard/system',
            iconLoaded: false
          },
          {
            id: 'server',
            name: '服务器情况',
            icon: '/static/images/icons/server.png',
            path: '/pages/dashboard/server',
            iconLoaded: false
          },
          {
            id: 'assets',
            name: '资金情况',
            icon: '/static/images/icons/money.png',
            path: '/pages/dashboard/assets',
            iconLoaded: false
          }
        ],
        // 项目列表数据，从后端接口获取
        projects: [],
        chartGrid: {
          left: 10,
          right: 10,
          top: 10,
          bottom: 10,
        },
        lineChartExtra: {
          series: {
            smooth: false,
          }
        },
        lineData: {
          columns: ['时间', '工单数量'],
          rows: [
            { '时间': '08:00', '工单数量': 100 },
            { '时间': '09:00', '工单数量': 150 },
            { '时间': '10:00', '工单数量': 200 },
            { '时间': '11:00', '工单数量': 180 },
            { '时间': '12:00', '工单数量': 80 },
            { '时间': '13:00', '工单数量': 110 },
            { '时间': '14:00', '工单数量': 250 },
            { '时间': '15:00', '工单数量': 210 },
          ],
        },
        ringData: {
          columns: ['类型', '数量'],
          rows: [
            { '类型': '类型1', '数量': 100 },
            { '类型': '类型2', '数量': 150 },
            { '类型': '类型3', '数量': 200 },
          ]
        },
        ringChartExtra: {
          legend: {
            bottom: 0,
          }
        },
        ringChartSettings: {
          radius: ['40%', '60%'],
          offsetY: 100,
        }
      }
    },

    // 生命周期方法
    mounted() {
      // 组件加载时初始化当前日期
      this.initCurrentDate()
      // 加载项目数据
      this.loadProjects()
      // 加载初始人员数据（不传项目ID，获取全部统计）
      this.loadUserCountData()
    },

    // 组件方法
    methods: {
      /**
       * 初始化当前日期
       * 格式化为 YYYY-MM-DD 格式
       */
      initCurrentDate() {
        const now = new Date()
        const year = now.getFullYear()
        const month = String(now.getMonth() + 1).padStart(2, '0')
        const day = String(now.getDate()).padStart(2, '0')

        this.currentDate = `${year}-${month}-${day}`

        console.log('初始化当前日期:', this.currentDate)
      },

      /**
       * 切换菜单显示状态
       */
      toggleMenu() {
        this.showMenu = !this.showMenu
        console.log('切换菜单状态:', this.showMenu)
      },

      /**
       * 关闭菜单
       */
      closeMenu() {
        this.showMenu = false
        console.log('关闭菜单')
      },

      /**
       * 导航到指定页面
       * @param {Object} menuItem - 菜单项对象
       */
      navigateToPage(menuItem) {
        console.log('导航到页面:', menuItem.name, menuItem.path)

        // 关闭菜单
        this.closeMenu()

        // 导航到对应页面
        uni.navigateTo({
          url: menuItem.path,
          success: () => {
            console.log('导航成功:', menuItem.path)
          },
          fail: (err) => {
            console.error('导航失败:', err)
            uni.showToast({
              title: '页面跳转失败',
              icon: 'none'
            })
          }
        })
      },

      /**
       * 图标加载成功
       */
      onImageLoad(e) {
        console.log('图标加载成功:', e)
        // 可以在这里设置图标加载状态
      },

      /**
       * 图标加载失败
       */
      onImageError(e) {
        console.error('图标加载失败:', e)
      },

      /**
       * 获取备用图标文字
       */
      getIconText(id) {
        const iconMap = {
          'person': '👥',
          'ticket': '📄',
          'system': '⚙️',
          'server': '🖥️',
          'assets': '💰'
        }
        return iconMap[id] || '📋'
      },

      /**
       * 加载项目数据
       */
      async loadProjects() {
        try {
          this.projectsLoading = true
          console.log('开始加载项目数据...')

          const response = await getAllProjects()
          console.log('项目数据加载成功:', response)

          if (response && response.code === 200 && response.data) {
            // 打印原始数据结构，便于调试
            console.log('原始项目数据结构:', JSON.stringify(response.data, null, 2))

            // 处理接口返回的数据结构，适配多种可能的ID字段名
            this.projects = response.data.map((project, index) => {
              console.log(`项目 ${index} 原始数据:`, project)

              // 尝试多种可能的ID字段名
              const projectId = project.projectId || project.id || project.ID || project.Id || project.projectID
              const projectName = project.projectName || project.name || project.projectTitle || project.title

              if (!projectId) {
                console.warn(`项目 ${index} 缺少ID字段，原始数据:`, project)
              }

              const processedProject = {
                id: projectId,
                name: projectName,
                // 保存原始的项目ID字段，以备后用
                originalProjectId: project.projectId,
                originalId: project.id,
                // 保存完整的项目信息，可能包含服务器、系统、人员等信息
                ...project
              }

              console.log(`项目 ${index} 处理后:`, processedProject)
              console.log(`项目 ${index} 最终ID:`, processedProject.id)
              return processedProject
            })

            console.log('最终处理后的项目数据:', this.projects)

            // 项目数据加载完成，保持默认的"请选择"状态
            // 让用户自行选择项目
          } else {
            console.warn('项目数据格式异常:', response)
            uni.showToast({
              title: '项目数据加载异常',
              icon: 'none'
            })
          }
        } catch (error) {
          console.error('加载项目数据失败:', error)
          uni.showToast({
            title: '项目数据加载失败',
            icon: 'none'
          })
        } finally {
          this.projectsLoading = false
        }
      },

      /**
       * 项目选择改变处理
       * @param {Object} e - 选择器事件对象
       */
      onProjectChange(e) {
        const index = e.detail.value
        console.log('项目选择改变:', index, '项目总数:', this.projects.length)
        console.log('当前项目列表:', this.projects)

        if (index >= 0 && index < this.projects.length) {
          this.selectedProjectIndex = index
          this.selectedProject = this.projects[index]

          console.log('选中项目完整信息:', JSON.stringify(this.selectedProject, null, 2))
          console.log('选中项目ID:', this.selectedProject.id)
          console.log('选中项目名称:', this.selectedProject.name)

          // 触发项目选中后的处理
          this.onProjectSelected(this.selectedProject)
        } else {
          console.warn('选择的项目索引无效:', index)
          console.warn('可用项目列表:', this.projects)

          // 如果索引无效，清除选择状态
          this.selectedProjectIndex = -1
          this.selectedProject = null
        }
      },

      /**
       * 项目选中后的处理
       * @param {Object} project - 选中的项目对象
       */
      onProjectSelected(project) {
        console.log('项目选中后处理 - 完整项目对象:', JSON.stringify(project, null, 2))

        if (!project) {
          console.warn('选中的项目对象为空')
          return
        }

        // 检查项目ID的各种可能字段，优先使用原始字段
        const projectId = project.id || project.originalProjectId || project.projectId || project.originalId || project.ID || project.Id
        console.log('提取的项目ID:', projectId)
        console.log('项目ID类型:', typeof projectId)
        console.log('项目对象中的所有ID相关字段:')
        console.log('  - project.id:', project.id)
        console.log('  - project.projectId:', project.projectId)
        console.log('  - project.originalProjectId:', project.originalProjectId)
        console.log('  - project.originalId:', project.originalId)

        if (!projectId) {
          console.error('无法获取项目ID，项目对象:', project)
          uni.showToast({
            title: '项目ID获取失败',
            icon: 'none'
          })
          return
        }

        // 显示选择成功提示
        uni.showToast({
          title: `已选择：${project.name}`,
          icon: 'success',
          duration: 1500
        })

        // 激活清除按钮的首次显示特效
        this.clearButtonFirstShow = true
        // 3秒后取消首次显示特效
        setTimeout(() => {
          this.clearButtonFirstShow = false
        }, 3000)

        // 根据选中的项目重新加载人员数据
        console.log('即将调用 loadUserCountData，传递项目ID:', projectId)
        this.loadUserCountData(projectId)

        // TODO: 可以在这里调用其他模块的接口
        // this.loadTicketData(projectId)
        // this.loadSystemData(projectId)
        // this.loadServerData(projectId)
        // this.loadAssetsData(projectId)

        console.log('当前选中项目ID:', projectId)
        console.log('当前选中项目名称:', project.name)
      },

      /**
       * 获取项目显示文本
       * @returns {String} 显示的文本
       */
      getProjectDisplayText() {
        if (this.projectsLoading) {
          return '加载中...'
        }

        if (this.selectedProject && this.selectedProject.name) {
          return this.selectedProject.name
        }

        if (this.projects.length === 0) {
          return '暂无项目'
        }

        return '请选择项目'
      },

      /**
       * 加载人员情况数据
       * @param {String} projectId - 项目ID，可选
       */
      async loadUserCountData(projectId = null) {
        try {
          this.userCountLoading = true
          console.log('=== 开始加载人员数据 ===')
          console.log('传入的项目ID:', projectId)
          console.log('项目ID类型:', typeof projectId)
          console.log('是否传递项目ID:', projectId ? '是' : '否')

          // 构建请求参数
          const requestParams = projectId ? { projectId } : {}
          console.log('接口请求参数:', requestParams)
          console.log('即将调用接口: /itsm/monitorData/getProjectUserCountInfo')

          const response = await getProjectUserCountInfo(projectId)
          console.log('人员数据接口响应:', JSON.stringify(response, null, 2))

          if (response && response.code === 200 && response.data) {
            // 处理接口返回的数据结构，适配可能的不同字段名
            const data = response.data
            this.userCountData = {
              totalCount: data.projectPeopleTotal || data.projectPeopleTotal || data.projectPeopleTotal || 0,
              onDutyCount: data.projectPeopleOnDutyNum || data.projectPeopleOnDutyNum || data.projectPeopleOnDutyNum || 0,
              leaveCount: data.projectPeopleLeaveNum || data.projectPeopleLeaveNum || data.projectPeopleLeaveNum || 0,
              abnormalCount: data.projectPeopleAbnormalNum || data.projectPeopleAbnormalNum || data.projectPeopleAbnormalNum || 0
            }
            console.log('处理后的人员数据:', JSON.stringify( this.userCountData,null,4))
          } else {
            console.warn('人员数据格式异常:', response)
            // 重置为默认值
            this.userCountData = {
              totalCount: 0,
              onDutyCount: 0,
              leaveCount: 0,
              abnormalCount: 0
            }

            uni.showToast({
              title: '人员数据加载异常',
              icon: 'none'
            })
          }
        } catch (error) {
          console.error('加载人员数据失败:', error)

          // 重置为默认值
          this.userCountData = {
            totalCount: 0,
            onDutyCount: 0,
            leaveCount: 0,
            abnormalCount: 0
          }

          uni.showToast({
            title: '人员数据加载失败',
            icon: 'none'
          })
        } finally {
          this.userCountLoading = false
        }
      },

      /**
       * 刷新人员数据
       * 根据当前选中的项目重新加载数据
       */
      refreshUserCountData() {
        const projectId = this.selectedProject ? this.selectedProject.id : null
        console.log('刷新人员数据, 项目ID:', projectId)
        this.loadUserCountData(projectId)
      },

      /**
       * 清除项目选择
       * 重置为未选择状态，并加载全部项目的统计数据
       */
      clearProjectSelection() {
        console.log('清除项目选择')

        // 重置项目选择状态
        this.selectedProjectIndex = -1
        this.selectedProject = null

        // 显示清除成功提示
        uni.showToast({
          title: '已清除项目选择',
          icon: 'success',
          duration: 1000
        })

        // 重新加载全部项目的人员数据
        console.log('重新加载全部项目的人员数据')
        this.loadUserCountData(null)

        // TODO: 可以在这里重新加载其他模块的全部数据
        // this.loadTicketData(null)
        // this.loadSystemData(null)
        // this.loadServerData(null)
        // this.loadAssetsData(null)
      },

      /**
       * 显示清除按钮提示
       * 长按时触发
       */
      showClearTip() {
        uni.showToast({
          title: '点击清除项目选择',
          icon: 'none',
          duration: 1500
        })
      }
    }
  }
</script>

<style lang="scss">
@import url("@/static/scss/dashboard.scss");

.dashboard-index {
  padding-bottom: 30rpx;
  background: url('@/static/images/dashboard_bg.png') no-repeat center top;
  background-size: contain;
  .top-box {
    width: 100%;
    padding: 214rpx 24rpx 34rpx 24rpx;

    // 头部导航栏
    .header-nav {
      position: relative;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30rpx;

      .logo {
        height: 56rpx;
      }

      // 菜单按钮
      .menu-button {
        position: relative;
        padding: 10rpx;
        cursor: pointer;
        z-index: 10000;

        .menu-icon {
          width: 32rpx;
          height: 32rpx;
          transition: all 0.3s ease;
        }

        // 按钮按下效果
        &:active {
          transform: scale(0.95);
        }
      }
    }

    // 项目选择器容器
    .project-selector-wrapper {
      position: relative;
    }

    .project-selector {
      color: #FFF;
      line-height: 80rpx;
      background: rgba(255, 255, 255, 0.2);
      padding: 0 24rpx;
      border-radius: 10rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      transition: all 0.3s ease;

      &.loading {
        opacity: 0.7;
        background: rgba(255, 255, 255, 0.1);
      }

      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      .selected-project {
        display: flex;
        align-items: center;
        flex: 1;
        overflow: hidden;

        .icon {
          width: 32rpx;
          height: 32rpx;
          flex-shrink: 0;
        }

        .text {
          margin: 0 10rpx;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      // 选择器操作区域
      .selector-actions {
        display: flex;
        align-items: center;
        gap: 16rpx; // 增大间距，增强视觉分离

        .clear-button {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 36rpx;
          height: 36rpx;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.9);
          backdrop-filter: blur(10rpx);
          box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          cursor: pointer;
          position: relative;
          // 增加点击区域，提升移动端体验
          padding: 6rpx;
          margin: -6rpx;
          // 现代化边框设计
          border: 1rpx solid rgba(255, 255, 255, 0.3);

          &:hover {
            background: rgba(255, 255, 255, 1);
            box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
            transform: translateY(-2rpx) scale(1.05);
          }

          &:active {
            background: rgba(240, 240, 240, 0.95);
            transform: scale(0.98) translateY(0);
            box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
          }

          .clear-icon {
            width: 20rpx;
            height: 20rpx;
            filter: brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%) contrast(97%);
            transition: all 0.2s ease;
          }

          &:hover .clear-icon {
            filter: brightness(0) saturate(100%) invert(13%) sepia(94%) saturate(7151%) hue-rotate(3deg) brightness(90%) contrast(118%);
            transform: rotate(90deg);
          }

          // 清除提示文字
          .clear-tip {
            position: absolute;
            top: -50rpx;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: #fff;
            padding: 8rpx 12rpx;
            border-radius: 6rpx;
            font-size: 24rpx;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.2s ease;
            z-index: 1000;

            &::after {
              content: '';
              position: absolute;
              top: 100%;
              left: 50%;
              transform: translateX(-50%);
              border: 6rpx solid transparent;
              border-top-color: rgba(0, 0, 0, 0.8);
            }
          }

          &:hover .clear-tip {
            opacity: 1;
          }
        }

        .dropdown-icon {
          width: 32rpx;
          height: 32rpx;
          flex-shrink: 0;

          &.rotate {
            animation: rotate 1s linear infinite;
          }
        }
      }
    }

    // 旋转动画
    @keyframes rotate {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }

    // 清除按钮淡入动画
    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: scale(0.8);
      }
      to {
        opacity: 1;
        transform: scale(1);
      }
    }

    // 脉冲动画，吸引用户注意
    @keyframes pulse {
      0% {
        box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.3);
      }
      50% {
        box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.6), 0 0 0 4rpx rgba(255, 71, 87, 0.2);
      }
      100% {
        box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.3);
      }
    }

    // 闪烁提示动画
    @keyframes blink {
      0%, 50% {
        opacity: 1;
      }
      25%, 75% {
        opacity: 0.7;
      }
    }

    .clear-button {
      animation: fadeIn 0.3s ease, pulse 2s ease-in-out infinite;

      // 首次出现时的闪烁提示
      &.first-show {
        animation: fadeIn 0.3s ease, blink 0.8s ease-in-out 3, pulse 2s ease-in-out infinite 2.4s;
      }
    }
  }
  .box {
    margin: 0 24rpx 24rpx 24rpx;

    // 加载状态样式
    .loading-container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 136rpx;

      .loading-text {
        color: #999;
        font-size: 28rpx;
      }
    }
    .title,
    .sub-title {
      display: flex;
      margin-bottom: 30rpx;
      .icon {
        width: 36rpx;
        height: 36rpx;
        margin-right: 10rpx;
      }
      .content {
        display: inline-flex;
        align-items: center;
        color: #333;
        font-size: 36rpx;
        font-weight: 600;
      }
      .extra {
        display: inline-flex;
        align-items: center;
        color: #999;
        margin-left: auto;
      }
    }
    .sub-title {
      margin: 30rpx 0;
      .content {
        &::before {
          content: '';
          display: inline-block;
          width: 7rpx;
          height: 30rpx;
          border-radius: 9rpx;
          background: #006ED9;
          margin-right: 16rpx;
        }
      }
      .tab-list {
        display: inline-flex;
        align-items: center;
        .item {
          color: #BBB;
          background: #FAFAFA;
          border-radius: 4rpx;
          line-height: 42rpx;
          padding: 0 16rpx;
          margin-left: 12rpx;
          &.active {
            color: #006ED9;
            background: #F2FAFF;
          }
        }
      }
    }
    .chart-wrapper {
      height: 460rpx;
    }
  }
  .card-row {
    .item {
      display: flex;
      position: relative;
      height: 136rpx;
      align-items: flex-end;
      color: #747474;
      padding: 16rpx;
      font-size: 28rpx;
      font-weight: 400;
      border-radius: 8rpx;
      .bg-icon {
        position: absolute;
        top: 8rpx;
        right: 10rpx;
        width: 50rpx;
        height: 50rpx;
        z-index: 0;
      }
      .content {
        position: relative;
        z-index: 1;
      }
      .num {
        color: #333;
        font-weight: bold;
        font-size: 36rpx;
      }
      &.primary {
        .num {
          font-size: 48rpx;
        }
      }
      &.blue {
        background: linear-gradient( 180deg, #BCE2FF 0%, #E7F4FF 100%);
        .label {
          color: #509CE4;
        }
      }
      &.green {
        background: linear-gradient( 360deg, #EFFFFB 0%, #DEFAED 100%);
       .label {
          color: #2BA97B;
        }
      }
      &.pink {
        background: linear-gradient( 180deg, #FFEBEB 0%, #FFF8F9 100%);
        .label {
          color: #EA6055;
        }
      }
      &.gray {
        background: linear-gradient( 180deg, #E9E9E9 0%, #F3F3F3 100%);
       .label {
          color: #747474;
        }
      }
    }
  }

  // 下拉菜单样式
  .dropdown-menu {
    position: absolute;
    top: 50rpx;
    right: -50rpx;
    width: 320rpx;
    background-color: #fff;
    border-radius: 8rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
    z-index: 10001;
    animation: dropdownFadeIn 0.2s ease;
    overflow: hidden;

    .menu-list {
      .menu-item {
        display: flex;
        align-items: center;
        padding: 24rpx 28rpx;
        border-bottom: 1rpx solid #f0f0f0;
        cursor: pointer;
        transition: background-color 0.2s ease;
        min-height: 80rpx;

        &:active {
          background-color: #f5f5f5;
        }

        .icon-wrapper {
          width: 36rpx;
          height: 36rpx;
          margin-right: 20rpx;
          flex-shrink: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;

          .menu-item-icon {
            width: 100%;
            height: 100%;
            display: block;
            object-fit: contain;
          }

          .icon-text {
            font-size: 28rpx;
            line-height: 1;
            text-align: center;
          }
        }

        .menu-text {
          flex: 1;
          font-size: 28rpx;
          color: #333;
          font-weight: 400;
          line-height: 1.2;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      // 最后一个菜单项去掉底部边框
      .menu-item:last-child {
        border-bottom: none;
      }
    }
  }

  // 菜单遮罩层
  .menu-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10000;
    background-color: transparent;
  }

  // 动画定义
  @keyframes dropdownFadeIn {
    from {
      opacity: 0;
      transform: translateY(-10rpx);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}
</style>