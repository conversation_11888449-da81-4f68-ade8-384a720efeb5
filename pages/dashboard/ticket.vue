<template>
  <view class="viewport ticket">
    <view class="page-container">
      <!-- 页面头部，包含返回按钮和标题 -->
      <view class="page-header">
        <view class="back-button" @click="goBack">
          <image class="back-icon" mode="aspectFit" src="/static/images/icons/close.png" />
        </view>
        <view class="page-title">工单情况</view>
        <view class="placeholder"></view>
      </view>
      <view class="search-box">
        <uni-row :gutter="10">
          <uni-col :span="12">
            <view class="search-item">
              <!-- <image class="icon" mode="aspectFit" src="@/static/images/icons/search.png" /> -->
              <uni-icons class="uni-icon" type="search" color="#BBB" size="22"></uni-icons>
              <view class="search-input">
                <uni-combox :border="false" :candidates="persons" placeholder="工单编号/名称/处理人"></uni-combox>
              </view>
            </view>
          </uni-col>
          <uni-col :span="12">
            <view class="search-item">
              <uni-data-select
                v-model="searchParams.status"
                placeholder="状态"
                :border="false" 
                :localdata="range"
                @change="change"
              ></uni-data-select>
            </view>
          </uni-col>
          <uni-col :span="12">
            <view class="search-item">
              <view class="search-input">
                <uni-datetime-picker
                  :border="false"
                  v-model="searchParams.date"
                  type="daterange"
                  start="2021-3-20"
                  end="2021-5-20"
                  rangeSeparator="至"
                />
              </view>
            </view>
          </uni-col>
          <uni-col :span="12">
            <view class="search-item">
              <uni-data-select
                v-model="searchParams.department"
                placeholder="部门"
                :border="false" 
                :localdata="range"
                @change="change"
              ></uni-data-select>
            </view>
          </uni-col>
        </uni-row>
      </view>
      <view class="box" :class="item.status" v-for="(item, index) in statusList" :key="index">
        <view class="title">
          <view class="content">
            <image v-if="item.status === 'pending'" class="icon" mode="aspectFit" src="@/static/images/icons/clock2.png" />
            <image v-if="item.status === 'processing'" class="icon" mode="aspectFit" src="@/static/images/icons/transfer.png" />
            <image v-if="item.status === 'timeout'" class="icon" mode="aspectFit" src="@/static/images/icons/warning2.png" />
            <image v-if="item.status === 'completed'" class="icon" mode="aspectFit" src="@/static/images/icons/check.png" />
            <image v-if="item.status === 'cancelled'" class="icon" mode="aspectFit" src="@/static/images/icons/close.png" />
            <text class="text">{{ item.text }}</text>
          </view>
          <view class="extra">
            <text class="date">{{ currentDate }}</text>
          </view>
        </view>
        <view class="body">
          <view class="info-list">
            <view class="item">
              <view class="name">
                <text class="text">工单编号</text>
              </view>
              <view class="value">
                <text class="text">ZSJ0001</text>
              </view>
            </view>
            <view class="item">
              <view class="name">
                <text class="text">工单名称</text>
              </view>
              <view class="value">
                <text class="text">XXX工作任务内容文字</text>
              </view>
            </view>
            <view class="item">
              <view class="name">
                <text class="text">部门/处理人</text>
              </view>
              <view class="value">
                <text class="text">京信股份  张三</text>
              </view>
            </view>
            <view class="item multi-line">
              <view class="name">
                <text class="text">任务内容</text>
              </view>
              <view class="value">
                <text class="text">处理XX系统的数据，形成统计数限制形成限制形成统计字数限制数形成统计数限制形成限制形成统计字数限制数</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  name: 'PersonPage',
  data() {
    return {
      // 当前日期，格式：YYYY-MM-DD
      currentDate: '',
      // statusList: ['pending', 'processing', 'timeout', 'completed', 'cancelled'],
      statusList: [
        { status: 'pending', text: '待受理' },
        { status: 'processing', text: '处理中' },
        { status: 'timeout', text: '超时' },
        { status: 'completed', text: '已完成' },
        { status: 'cancelled', text: '已取消' }
      ],
      persons: ['张三', '李四', '王五', '赵六'],
      range: [
        { text: '选项0', value: 0 },
        { text: '选项1', value: 1 },
        { text: '选项2', value: 2 }
      ],
      searchParams: {
        name: '',
        status: undefined,
        date: '',
        department: ''
      }
    }
  },

  // 生命周期方法
  mounted() {
    // 组件加载时初始化当前日期
    this.initCurrentDate()
  },

  // 组件方法
  methods: {
    /**
     * 初始化当前日期
     * 格式化为 YYYY-MM-DD 格式
     */
    initCurrentDate() {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')

      this.currentDate = `${year}-${month}-${day}`

      console.log('Ticket页面初始化当前日期:', this.currentDate)
    },

    /**
     * 智能返回上一页功能
     *
     * 问题背景：
     * 当用户从首页菜单进入子页面后，如果在子页面进行刷新操作，会导致页面栈被重置，
     * 此时使用 uni.navigateBack() 会失败，因为页面栈中只有当前页面，没有上一页可以返回。
     *
     * 解决方案：
     * 1. 使用 getCurrentPages() 检测当前页面栈状态
     * 2. 如果页面栈长度 > 1，说明有导航历史，使用 navigateBack 正常返回
     * 3. 如果页面栈长度 = 1，说明是刷新后的状态或直接访问，使用 reLaunch 跳转到首页
     *
     * 页面栈状态说明：
     * - 正常导航：[首页, 当前页面] → 长度为2，可以正常返回
     * - 刷新后：[当前页面] → 长度为1，需要跳转到首页
     * - 直接访问：[当前页面] → 长度为1，需要跳转到首页
     */
    goBack() {
      console.log('=== 开始智能返回操作 ===')

      // 获取当前页面栈，用于判断导航状态
      const pages = getCurrentPages()
      console.log('当前页面栈长度:', pages.length)
      console.log('当前页面栈路由:', pages.map(page => page.route))

      // 判断页面栈状态，决定返回策略
      if (pages.length > 1) {
        // 页面栈中有多个页面，说明是正常导航进入的，可以使用 navigateBack 返回
        console.log('检测到正常导航状态，使用 navigateBack 返回上一页')
        uni.navigateBack({
          delta: 1, // 返回上一页
          success: () => {
            console.log('navigateBack 返回成功')
          },
          fail: (err) => {
            console.error('navigateBack 返回失败，可能的原因：页面栈状态异常', err)
            // 如果 navigateBack 失败，使用备用方案
            this.fallbackToHome()
          }
        })
      } else {
        // 页面栈中只有当前页面，说明是刷新后的状态或直接访问
        console.log('检测到页面栈被重置（刷新或直接访问），直接跳转到首页')
        this.fallbackToHome()
      }
    },

    /**
     * 备用返回方案：重新启动应用并跳转到首页
     *
     * 使用场景：
     * 1. 页面栈被重置后的返回操作
     * 2. navigateBack 失败时的降级处理
     *
     * 为什么使用 reLaunch 而不是 navigateTo：
     * - reLaunch 会关闭所有页面并重新启动应用，确保页面栈状态正确
     * - navigateTo 只是简单跳转，可能导致页面栈状态混乱
     */
    fallbackToHome() {
      console.log('=== 执行备用返回方案：重启应用并跳转到首页 ===')
      uni.reLaunch({
        url: '/pages/dashboard/index',
        success: () => {
          console.log('重启应用并跳转到首页成功')
        },
        fail: (err) => {
          console.error('跳转到首页失败，这是一个严重错误:', err)
          // 如果连 reLaunch 都失败了，给用户提示
          uni.showToast({
            title: '返回失败，请重启应用',
            icon: 'none',
            duration: 3000
          })
        }
      })
    },

    /**
     * 状态改变处理
     */
    change(e) {
      console.log('状态改变:', e)
    }
  }
}
</script>
<style lang="scss">
@import url("@/static/scss/dashboard.scss");

.ticket{
  // 页面头部样式
  .page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 24rpx;
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
    color: #fff;

    .back-button {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 50%;
      transition: background-color 0.2s ease;

      &:active {
        background-color: rgba(255, 255, 255, 0.2);
      }

      .back-icon {
        width: 32rpx;
        height: 32rpx;
        filter: brightness(0) invert(1);
      }
    }

    .page-title {
      flex: 1;
      text-align: center;
      font-size: 36rpx;
      font-weight: 600;
      margin: 0;
    }

    .placeholder {
      width: 60rpx;
      height: 60rpx;
    }
  }

  .box {
    padding: 0;
    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 68rpx;
      .content {
        display: inline-flex;
        align-items: center;
        color: #FFF;
        padding: 0 10rpx 10rpx 10rpx;
      }
      .icon {
        width: 36rpx;
        height: 36rpx;
        margin-right: 10rpx;
      }
      .date {
        color: #BBB;
        padding: 0 30rpx;
      }
    }
    .body {
      padding: 10rpx 24rpx;
    }
    .info-list {
      .multi-line {
        .value {
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2; /* 限制显示2行 */
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
    &.pending {
      .title {
        background: url("@/static/images/dashboard/title_bg_01.png") no-repeat left center;
        background-size: contain;
      }
    }
    &.processing {
      .title {
        background: url("@/static/images/dashboard/title_bg_02.png") no-repeat left center;
        background-size: contain;
      }
    }
    &.timeout {
      .title {
        background: url("@/static/images/dashboard/title_bg_03.png") no-repeat left center;
        background-size: contain;
      }
    }
    &.completed {
      .title {
        background: url("@/static/images/dashboard/title_bg_04.png") no-repeat left center;
        background-size: contain;
      }
    }
    &.cancelled {
      .title {
        background: url("@/static/images/dashboard/title_bg_05.png") no-repeat left center;
        background-size: contain;
      }
    }
  }
}
</style>