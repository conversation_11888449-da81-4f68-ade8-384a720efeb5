<template>
  <view class="viewport ticket">
    <view class="page-container">
      <!-- 页面头部，包含返回按钮和标题 -->
      <view class="page-header">
        <view class="back-button" @click="goBack">
          <image class="back-icon" mode="aspectFit" src="/static/images/icons/close.png" />
        </view>
        <view class="page-title">工单情况</view>
        <view class="placeholder"></view>
      </view>
      <view class="search-box">
        <uni-row :gutter="10">
          <uni-col :span="12">
            <view class="search-item">
              <!-- <image class="icon" mode="aspectFit" src="@/static/images/icons/search.png" /> -->
              <uni-icons class="uni-icon" type="search" color="#BBB" size="22"></uni-icons>
              <view class="search-input">
                <uni-combox :border="false" :candidates="persons" placeholder="工单编号/名称/处理人"></uni-combox>
              </view>
            </view>
          </uni-col>
          <uni-col :span="12">
            <view class="search-item">
              <uni-data-select
                v-model="searchParams.status"
                placeholder="状态"
                :border="false" 
                :localdata="range"
                @change="change"
              ></uni-data-select>
            </view>
          </uni-col>
          <uni-col :span="12">
            <view class="search-item">
              <view class="search-input">
                <uni-datetime-picker
                  :border="false"
                  v-model="searchParams.date"
                  type="daterange"
                  start="2021-3-20"
                  end="2021-5-20"
                  rangeSeparator="至"
                />
              </view>
            </view>
          </uni-col>
          <uni-col :span="12">
            <view class="search-item">
              <uni-data-select
                v-model="searchParams.department"
                placeholder="部门"
                :border="false" 
                :localdata="range"
                @change="change"
              ></uni-data-select>
            </view>
          </uni-col>
        </uni-row>
      </view>
      <view class="box" :class="item.status" v-for="(item, index) in statusList" :key="index">
        <view class="title">
          <view class="content">
            <image v-if="item.status === 'pending'" class="icon" mode="aspectFit" src="@/static/images/icons/clock2.png" />
            <image v-if="item.status === 'processing'" class="icon" mode="aspectFit" src="@/static/images/icons/transfer.png" />
            <image v-if="item.status === 'timeout'" class="icon" mode="aspectFit" src="@/static/images/icons/warning2.png" />
            <image v-if="item.status === 'completed'" class="icon" mode="aspectFit" src="@/static/images/icons/check.png" />
            <image v-if="item.status === 'cancelled'" class="icon" mode="aspectFit" src="@/static/images/icons/close.png" />
            <text class="text">{{ item.text }}</text>
          </view>
          <view class="extra">
            <text class="date">{{ currentDate }}</text>
          </view>
        </view>
        <view class="body">
          <view class="info-list">
            <view class="item">
              <view class="name">
                <text class="text">工单编号</text>
              </view>
              <view class="value">
                <text class="text">ZSJ0001</text>
              </view>
            </view>
            <view class="item">
              <view class="name">
                <text class="text">工单名称</text>
              </view>
              <view class="value">
                <text class="text">XXX工作任务内容文字</text>
              </view>
            </view>
            <view class="item">
              <view class="name">
                <text class="text">部门/处理人</text>
              </view>
              <view class="value">
                <text class="text">京信股份  张三</text>
              </view>
            </view>
            <view class="item multi-line">
              <view class="name">
                <text class="text">任务内容</text>
              </view>
              <view class="value">
                <text class="text">处理XX系统的数据，形成统计数限制形成限制形成统计字数限制数形成统计数限制形成限制形成统计字数限制数</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  name: 'PersonPage',
  data() {
    return {
      // 当前日期，格式：YYYY-MM-DD
      currentDate: '',
      // statusList: ['pending', 'processing', 'timeout', 'completed', 'cancelled'],
      statusList: [
        { status: 'pending', text: '待受理' },
        { status: 'processing', text: '处理中' },
        { status: 'timeout', text: '超时' },
        { status: 'completed', text: '已完成' },
        { status: 'cancelled', text: '已取消' }
      ],
      persons: ['张三', '李四', '王五', '赵六'],
      range: [
        { text: '选项0', value: 0 },
        { text: '选项1', value: 1 },
        { text: '选项2', value: 2 }
      ],
      searchParams: {
        name: '',
        status: undefined,
        date: '',
        department: ''
      }
    }
  },

  // 生命周期方法
  mounted() {
    // 组件加载时初始化当前日期
    this.initCurrentDate()
  },

  // 组件方法
  methods: {
    /**
     * 初始化当前日期
     * 格式化为 YYYY-MM-DD 格式
     */
    initCurrentDate() {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')

      this.currentDate = `${year}-${month}-${day}`

      console.log('Ticket页面初始化当前日期:', this.currentDate)
    },

    /**
     * 返回上一页
     * 智能返回逻辑：优先使用 navigateBack，失败时跳转到首页
     */
    goBack() {
      console.log('开始返回操作')

      // 获取当前页面栈
      const pages = getCurrentPages()
      console.log('当前页面栈长度:', pages.length)
      console.log('当前页面栈:', pages.map(page => page.route))

      // 如果页面栈长度大于1，说明有上一页可以返回
      if (pages.length > 1) {
        console.log('页面栈中有上一页，使用 navigateBack 返回')
        uni.navigateBack({
          delta: 1,
          success: () => {
            console.log('navigateBack 返回成功')
          },
          fail: (err) => {
            console.error('navigateBack 返回失败:', err)
            this.fallbackToHome()
          }
        })
      } else {
        console.log('页面栈中没有上一页，直接跳转到首页')
        this.fallbackToHome()
      }
    },

    /**
     * 备用返回方案：跳转到首页
     */
    fallbackToHome() {
      console.log('使用备用方案跳转到首页')
      uni.reLaunch({
        url: '/pages/dashboard/index',
        success: () => {
          console.log('跳转到首页成功')
        },
        fail: (err) => {
          console.error('跳转到首页失败:', err)
          uni.showToast({
            title: '返回失败，请重试',
            icon: 'none'
          })
        }
      })
    },

    /**
     * 状态改变处理
     */
    change(e) {
      console.log('状态改变:', e)
    }
  }
}
</script>
<style lang="scss">
@import url("@/static/scss/dashboard.scss");

.ticket{
  // 页面头部样式
  .page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 24rpx;
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
    color: #fff;

    .back-button {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 50%;
      transition: background-color 0.2s ease;

      &:active {
        background-color: rgba(255, 255, 255, 0.2);
      }

      .back-icon {
        width: 32rpx;
        height: 32rpx;
        filter: brightness(0) invert(1);
      }
    }

    .page-title {
      flex: 1;
      text-align: center;
      font-size: 36rpx;
      font-weight: 600;
      margin: 0;
    }

    .placeholder {
      width: 60rpx;
      height: 60rpx;
    }
  }

  .box {
    padding: 0;
    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 68rpx;
      .content {
        display: inline-flex;
        align-items: center;
        color: #FFF;
        padding: 0 10rpx 10rpx 10rpx;
      }
      .icon {
        width: 36rpx;
        height: 36rpx;
        margin-right: 10rpx;
      }
      .date {
        color: #BBB;
        padding: 0 30rpx;
      }
    }
    .body {
      padding: 10rpx 24rpx;
    }
    .info-list {
      .multi-line {
        .value {
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2; /* 限制显示2行 */
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
    &.pending {
      .title {
        background: url("@/static/images/dashboard/title_bg_01.png") no-repeat left center;
        background-size: contain;
      }
    }
    &.processing {
      .title {
        background: url("@/static/images/dashboard/title_bg_02.png") no-repeat left center;
        background-size: contain;
      }
    }
    &.timeout {
      .title {
        background: url("@/static/images/dashboard/title_bg_03.png") no-repeat left center;
        background-size: contain;
      }
    }
    &.completed {
      .title {
        background: url("@/static/images/dashboard/title_bg_04.png") no-repeat left center;
        background-size: contain;
      }
    }
    &.cancelled {
      .title {
        background: url("@/static/images/dashboard/title_bg_05.png") no-repeat left center;
        background-size: contain;
      }
    }
  }
}
</style>