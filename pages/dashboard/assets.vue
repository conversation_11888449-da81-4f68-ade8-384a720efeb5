<template>
  <view class="viewport assets">
    <view class="page-container">
      <!-- 页面头部，包含返回按钮和标题 -->
      <view class="page-header">
        <view class="back-button" @click="goBack">
          <image class="back-icon" mode="aspectFit" src="/static/images/icons/close.png" />
        </view>
        <view class="page-title">资金情况</view>
        <view class="placeholder"></view>
      </view>
      <view class="box overview">
        <uni-row class="total" :gutter="24">
          <uni-col :span="6">
            <text>共 2 笔</text>
          </uni-col>
          <uni-col :span="18">
            <view class="total-amount">
              <text>总金额</text>
              <view class="amount">
                <text class="number">1,234,567.89</text>
                <text class="unit">万元</text>
              </view>
            </view>
          </uni-col>
        </uni-row>
        <view class="cards">
          <uni-row :gutter="24">
            <uni-col :span="12">
              <view class="item paid">
                <view class="label">
                  <text>已支付</text>
                </view>
                <view class="amount">
                  <text class="number">1,234,567.89</text>
                  <text class="unit">万元</text>
                </view>
                <view class="percent">
                  <text>占比</text>
                  <text class="number">50%</text>
                </view>
              </view>
            </uni-col>
            <uni-col :span="12">
              <view class="item unpaid">
                <view class="label">
                  <text>未支付</text>
                </view>
                <view class="amount">
                  <text class="number">1,234,567.89</text>
                  <text class="unit">万元</text>
                </view>
                <view class="percent">
                  <text>占比</text>
                  <text class="number">50%</text>
                </view>
              </view>
            </uni-col>
          </uni-row>
        </view>
      </view>
      <view class="box" v-for="(item, index) in 2" :key="index">
        <view class="title">
          <view class="content">
            <image class="icon" mode="aspectFit" src="@/static/images/icons/money.png" />
            <text class="text">第 {{ index + 1 }} 笔</text>
          </view>
        </view>
        <view class="body">
          <view class="info-list">
            <view class="item">
              <view class="name">
                <text class="text">支付金额</text>
              </view>
              <view class="value">
                <text class="text">75.135万元</text>
              </view>
            </view>
            <view class="item">
              <view class="name">
                <text class="text">状 态</text>
              </view>
              <view class="value">
                <text class="text blue">已支付</text>
                <!-- <text class="text orange">未支付</text> -->
              </view>
            </view>
            <view class="item">
              <view class="name">
                <text class="text">支付时间</text>
              </view>
              <view class="value">
                <text class="text">2025-3-21 10:10:32</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'AssetsPage',
  data() {
    return {
      // statusList: ['pending', 'processing', 'timeout', 'completed', 'cancelled'],
      statusList: [
        { status: 'pending', text: '待受理' },
        { status: 'processing', text: '处理中' },
        { status: 'timeout', text: '超时' },
        { status: 'completed', text: '已完成' },
        { status: 'cancelled', text: '已取消' }
      ],
      persons: ['张三', '李四', '王五', '赵六'],
      range: [
        { text: '选项0', value: 0 },
        { text: '选项1', value: 1 },
        { text: '选项2', value: 2 }
      ],
      searchParams: {
        name: '',
        status: undefined,
        date: '',
        department: ''
      }
    }
  },
  methods: {
    /**
     * 智能返回上一页功能
     *
     * 问题背景：
     * 当用户从首页菜单进入子页面后，如果在子页面进行刷新操作，会导致页面栈被重置，
     * 此时使用 uni.navigateBack() 会失败，因为页面栈中只有当前页面，没有上一页可以返回。
     *
     * 解决方案：
     * 1. 使用 getCurrentPages() 检测当前页面栈状态
     * 2. 如果页面栈长度 > 1，说明有导航历史，使用 navigateBack 正常返回
     * 3. 如果页面栈长度 = 1，说明是刷新后的状态或直接访问，使用 reLaunch 跳转到首页
     *
     * 页面栈状态说明：
     * - 正常导航：[首页, 当前页面] → 长度为2，可以正常返回
     * - 刷新后：[当前页面] → 长度为1，需要跳转到首页
     * - 直接访问：[当前页面] → 长度为1，需要跳转到首页
     */
    goBack() {
      console.log('=== 开始智能返回操作 ===')

      // 获取当前页面栈，用于判断导航状态
      const pages = getCurrentPages()
      console.log('当前页面栈长度:', pages.length)
      console.log('当前页面栈路由:', pages.map(page => page.route))

      // 判断页面栈状态，决定返回策略
      if (pages.length > 1) {
        // 页面栈中有多个页面，说明是正常导航进入的，可以使用 navigateBack 返回
        console.log('检测到正常导航状态，使用 navigateBack 返回上一页')
        uni.navigateBack({
          delta: 1, // 返回上一页
          success: () => {
            console.log('navigateBack 返回成功')
          },
          fail: (err) => {
            console.error('navigateBack 返回失败，可能的原因：页面栈状态异常', err)
            // 如果 navigateBack 失败，使用备用方案
            this.fallbackToHome()
          }
        })
      } else {
        // 页面栈中只有当前页面，说明是刷新后的状态或直接访问
        console.log('检测到页面栈被重置（刷新或直接访问），直接跳转到首页')
        this.fallbackToHome()
      }
    },

    /**
     * 备用返回方案：重新启动应用并跳转到首页
     *
     * 使用场景：
     * 1. 页面栈被重置后的返回操作
     * 2. navigateBack 失败时的降级处理
     *
     * 为什么使用 reLaunch 而不是 navigateTo：
     * - reLaunch 会关闭所有页面并重新启动应用，确保页面栈状态正确
     * - navigateTo 只是简单跳转，可能导致页面栈状态混乱
     */
    fallbackToHome() {
      console.log('=== 执行备用返回方案：重启应用并跳转到首页 ===')
      uni.reLaunch({
        url: '/pages/dashboard/index',
        success: () => {
          console.log('重启应用并跳转到首页成功')
        },
        fail: (err) => {
          console.error('跳转到首页失败，这是一个严重错误:', err)
          // 如果连 reLaunch 都失败了，给用户提示
          uni.showToast({
            title: '返回失败，请重启应用',
            icon: 'none',
            duration: 3000
          })
        }
      })
    }
  }
}
</script>

<style lang="scss">
@import url("@/static/scss/dashboard.scss");

.assets {
  // 页面头部样式
  .page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 24rpx;
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
    color: #fff;

    .back-button {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 50%;
      transition: background-color 0.2s ease;

      &:active {
        background-color: rgba(255, 255, 255, 0.2);
      }

      .back-icon {
        width: 32rpx;
        height: 32rpx;
        filter: brightness(0) invert(1);
      }
    }

    .page-title {
      flex: 1;
      text-align: center;
      font-size: 36rpx;
      font-weight: 600;
      margin: 0;
    }

    .placeholder {
      width: 60rpx;
      height: 60rpx;
    }
  }

  .box {
    padding: 0;
    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 30rpx 20rpx 20rpx 20rpx;
      border-bottom: 1px solid #E9E9E9;
      .content {
        font-size: 30rpx;
        display: inline-flex;
        align-items: center;
        color: #747474;
        padding: 0 10rpx 10rpx 10rpx;
      }
      .icon {
        width: 36rpx;
        height: 36rpx;
        margin-right: 10rpx;
      }
      .date {
        color: #BBB;
        padding: 0 30rpx;
      }
    }
    .body {
      padding: 10rpx 24rpx;
    }
    .info-list {
      .multi-line {
        .value {
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2; /* 限制显示2行 */
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      .blue {
        color: #006ED9;
      }
      .orange {
        color: #FF9E49; 
      }
    }
    &.overview {
      margin-top: 30rpx;
      padding: 30rpx;
      font-size: 28rpx;
      color: #333;
      .amount {
        padding: 30rpx 0;
        .number {
          font-size: 64rpx;
          font-weight: bold;
          margin-right: 10rpx;
        }
      }
      .cards {
        margin-top: 30rpx;
        .item {
          color: #FFF;
          width: 100%;
          padding: 24rpx;
          background: #368FFD url("@/static/images/icons/paid.png") no-repeat top 10px right 10px;
          background-size: 68rpx 68rpx;
          border-radius: 8rpx;
          min-height: 180rpx;
          &.unpaid {
            background: #FF9E49 url("@/static/images/icons/unpaid.png") no-repeat top 10px right 10px;
            background-size: 68rpx 68rpx;
          }
          .amount {
            display: inline-flex;
            align-items: flex-end;
            .number {
              font-size: 42rpx;
              max-width: calc(100% - 36rpx);
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .unit {
              font-size: 24rpx;
              flex: 0 0 50rpx;
            }
          }
          .percent {
            font-size: 24rpx;
            .number {
              font-size: 32rpx;
              font-weight: bold;
              margin-left: 10rpx;
            }
          }
        }
      }
    }
  }
}
</style>