<template>
  <view class="viewport assets">
    <view class="page-container">
      <!-- 页面头部，包含返回按钮和标题 -->
      <view class="page-header">
        <view class="back-button" @click="goBack">
          <image class="back-icon" mode="aspectFit" src="/static/images/icons/close.png" />
        </view>
        <view class="page-title">资金情况</view>
        <view class="placeholder"></view>
      </view>
      <view class="box overview">
        <uni-row class="total" :gutter="24">
          <uni-col :span="6">
            <text>共 2 笔</text>
          </uni-col>
          <uni-col :span="18">
            <view class="total-amount">
              <text>总金额</text>
              <view class="amount">
                <text class="number">1,234,567.89</text>
                <text class="unit">万元</text>
              </view>
            </view>
          </uni-col>
        </uni-row>
        <view class="cards">
          <uni-row :gutter="24">
            <uni-col :span="12">
              <view class="item paid">
                <view class="label">
                  <text>已支付</text>
                </view>
                <view class="amount">
                  <text class="number">1,234,567.89</text>
                  <text class="unit">万元</text>
                </view>
                <view class="percent">
                  <text>占比</text>
                  <text class="number">50%</text>
                </view>
              </view>
            </uni-col>
            <uni-col :span="12">
              <view class="item unpaid">
                <view class="label">
                  <text>未支付</text>
                </view>
                <view class="amount">
                  <text class="number">1,234,567.89</text>
                  <text class="unit">万元</text>
                </view>
                <view class="percent">
                  <text>占比</text>
                  <text class="number">50%</text>
                </view>
              </view>
            </uni-col>
          </uni-row>
        </view>
      </view>
      <view class="box" v-for="(item, index) in 2" :key="index">
        <view class="title">
          <view class="content">
            <image class="icon" mode="aspectFit" src="@/static/images/icons/money.png" />
            <text class="text">第 {{ index + 1 }} 笔</text>
          </view>
        </view>
        <view class="body">
          <view class="info-list">
            <view class="item">
              <view class="name">
                <text class="text">支付金额</text>
              </view>
              <view class="value">
                <text class="text">75.135万元</text>
              </view>
            </view>
            <view class="item">
              <view class="name">
                <text class="text">状 态</text>
              </view>
              <view class="value">
                <text class="text blue">已支付</text>
                <!-- <text class="text orange">未支付</text> -->
              </view>
            </view>
            <view class="item">
              <view class="name">
                <text class="text">支付时间</text>
              </view>
              <view class="value">
                <text class="text">2025-3-21 10:10:32</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  name: 'AssetsPage',
  data() {
    return {
      // statusList: ['pending', 'processing', 'timeout', 'completed', 'cancelled'],
      statusList: [
        { status: 'pending', text: '待受理' },
        { status: 'processing', text: '处理中' },
        { status: 'timeout', text: '超时' },
        { status: 'completed', text: '已完成' },
        { status: 'cancelled', text: '已取消' }
      ],
      persons: ['张三', '李四', '王五', '赵六'],
      range: [
        { text: '选项0', value: 0 },
        { text: '选项1', value: 1 },
        { text: '选项2', value: 2 }
      ],
      searchParams: {
        name: '',
        status: undefined,
        date: '',
        department: ''
      }
    }
  },
  methods: {
    /**
     * 返回上一页
     */
    goBack() {
      uni.navigateBack({
        delta: 1,
        success: () => {
          console.log('返回成功')
        },
        fail: (err) => {
          console.error('返回失败:', err)
          // 如果返回失败，跳转到首页
          uni.reLaunch({
            url: '/pages/dashboard/index'
          })
        }
      })
    }
  }
}
</script>
<style lang="scss">
@import url("@/static/scss/dashboard.scss");

.assets {
  // 页面头部样式
  .page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 24rpx;
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
    color: #fff;

    .back-button {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 50%;
      transition: background-color 0.2s ease;

      &:active {
        background-color: rgba(255, 255, 255, 0.2);
      }

      .back-icon {
        width: 32rpx;
        height: 32rpx;
        filter: brightness(0) invert(1);
      }
    }

    .page-title {
      flex: 1;
      text-align: center;
      font-size: 36rpx;
      font-weight: 600;
      margin: 0;
    }

    .placeholder {
      width: 60rpx;
      height: 60rpx;
    }
  }

  .box {
    padding: 0;
    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 30rpx 20rpx 20rpx 20rpx;
      border-bottom: 1px solid #E9E9E9;
      .content {
        font-size: 30rpx;
        display: inline-flex;
        align-items: center;
        color: #747474;
        padding: 0 10rpx 10rpx 10rpx;
      }
      .icon {
        width: 36rpx;
        height: 36rpx;
        margin-right: 10rpx;
      }
      .date {
        color: #BBB;
        padding: 0 30rpx;
      }
    }
    .body {
      padding: 10rpx 24rpx;
    }
    .info-list {
      .multi-line {
        .value {
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2; /* 限制显示2行 */
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      .blue {
        color: #006ED9;
      }
      .orange {
        color: #FF9E49; 
      }
    }
    &.overview {
      margin-top: 30rpx;
      padding: 30rpx;
      font-size: 28rpx;
      color: #333;
      .amount {
        padding: 30rpx 0;
        .number {
          font-size: 64rpx;
          font-weight: bold;
          margin-right: 10rpx;
        }
      }
      .cards {
        margin-top: 30rpx;
        .item {
          color: #FFF;
          width: 100%;
          padding: 24rpx;
          background: #368FFD url("@/static/images/icons/paid.png") no-repeat top 10px right 10px;
          background-size: 68rpx 68rpx;
          border-radius: 8rpx;
          min-height: 180rpx;
          &.unpaid {
            background: #FF9E49 url("@/static/images/icons/unpaid.png") no-repeat top 10px right 10px;
            background-size: 68rpx 68rpx;
          }
          .amount {
            display: inline-flex;
            align-items: flex-end;
            .number {
              font-size: 42rpx;
              max-width: calc(100% - 36rpx);
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .unit {
              font-size: 24rpx;
              flex: 0 0 50rpx;
            }
          }
          .percent {
            font-size: 24rpx;
            .number {
              font-size: 32rpx;
              font-weight: bold;
              margin-left: 10rpx;
            }
          }
        }
      }
    }
  }
}
</style>