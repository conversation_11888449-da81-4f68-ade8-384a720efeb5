<template>
  <view class="viewport person">
    <view class="page-container">
      <!-- 页面头部，包含返回按钮和标题 -->
      <view class="page-header">
        <view class="back-button" @click="goBack">
          <image class="back-icon" mode="aspectFit" src="/static/images/icons/close.png" />
        </view>
        <view class="page-title">人员情况</view>
        <view class="placeholder"></view>
      </view>
      <view class="search-box">
        <uni-row :gutter="10">
          <uni-col :span="12">
            <view class="search-item">
              <!-- <image class="icon" mode="aspectFit" src="@/static/images/icons/search.png" /> -->
              <uni-icons class="uni-icon" type="search" color="#BBB" size="22"></uni-icons>
              <view class="search-input">
                <uni-combox :border="false" :candidates="persons" placeholder="人员姓名"></uni-combox>
              </view>
            </view>
          </uni-col>
          <uni-col :span="12">
            <view class="search-item">
              <uni-data-select
                v-model="searchParams.status"
                placeholder="状态"
                :border="false" 
                :localdata="range"
                @change="change"
              ></uni-data-select>
            </view>
          </uni-col>
          <uni-col :span="12">
            <view class="search-item">
              <view class="search-input">
                <uni-datetime-picker
                  :border="false"
                  v-model="searchParams.date"
                  type="daterange"
                  start="2021-3-20"
                  end="2021-5-20"
                  rangeSeparator="至"
                />
              </view>
            </view>
          </uni-col>
          <uni-col :span="12">
            <view class="search-item">
              <uni-data-select
                v-model="searchParams.department"
                placeholder="部门"
                :border="false" 
                :localdata="range"
                @change="change"
              ></uni-data-select>
            </view>
          </uni-col>
        </uni-row>
      </view>
      
      <view class="box">
        <view class="in-out-list">
          <view class="head">
            <view class="row">
              <view class="item name">姓名</view>
              <view class="item dept">部门</view>
              <view class="item in">签到</view>
              <view class="item out">签退</view>
              <view class="item date">日期</view>
            </view>
          </view>
          <view class="body">
            <view class="row">
              <view class="item name">
                <image class="icon" mode="aspectFit" src="@/static/images/icons/person2.png" />
                张三
              </view>
              <view class="item dept ellipsis">销售部</view>
              <view class="item in">
                <view class="status-tag status-1">迟到</view>
              </view>
              <view class="item out">
                <view class="status-tag status-4">已签退</view>
              </view>
              <view class="item date">03-06</view>
            </view>
            <view class="row">
              <view class="item name">
                <image class="icon" mode="aspectFit" src="@/static/images/icons/person2.png" />
                  刘关张三
              </view>
              <view class="item dept ellipsis">销售部销售部销售部</view>
              <view class="item in">
                <view class="status-tag status-1">未签到</view>
              </view>
              <view class="item out">
                <view class="status-tag status-4">已签退</view>
              </view>
              <view class="item date">03-06</view>
            </view>
            <view class="row">
              <view class="item name">
                <image class="icon" mode="aspectFit" src="@/static/images/icons/person2.png" />
                  刘关三
              </view>
              <view class="item dept ellipsis">销售部销售部销售部</view>
              <view class="item in">
                <view class="status-tag status-3">请假</view>
              </view>
              <view class="item out">
                <view class="status-tag status-4">已签退</view>
              </view>
              <view class="item date">03-06</view>
            </view>
            <view class="row">
              <view class="item name">
                <image class="icon" mode="aspectFit" src="@/static/images/icons/person2.png" />
                  刘关三
              </view>
              <view class="item dept ellipsis">销售部销售部销售部</view>
              <view class="item in">
                <view class="status-tag status-4">已签到</view>
              </view>
              <view class="item out">
                <view class="status-tag status-4">已签退</view>
              </view>
              <view class="item date">03-06</view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'PersonPage',
  data() {
    return {
      persons: ['张三', '李四', '王五', '赵六'],
      range: [
        { text: '选项0', value: 0 },
        { text: '选项1', value: 1 },
        { text: '选项2', value: 2 }
      ],
      searchParams: {
        name: '',
        status: undefined,
        date: '',
        department: ''
      }
    }
  },
  methods: {
    /**
     * 智能返回上一页功能
     *
     * 问题背景：
     * 当用户从首页菜单进入子页面后，如果在子页面进行刷新操作，会导致页面栈被重置，
     * 此时使用 uni.navigateBack() 会失败，因为页面栈中只有当前页面，没有上一页可以返回。
     *
     * 解决方案：
     * 1. 使用 getCurrentPages() 检测当前页面栈状态
     * 2. 如果页面栈长度 > 1，说明有导航历史，使用 navigateBack 正常返回
     * 3. 如果页面栈长度 = 1，说明是刷新后的状态或直接访问，使用 reLaunch 跳转到首页
     *
     * 页面栈状态说明：
     * - 正常导航：[首页, 当前页面] → 长度为2，可以正常返回
     * - 刷新后：[当前页面] → 长度为1，需要跳转到首页
     * - 直接访问：[当前页面] → 长度为1，需要跳转到首页
     */
    goBack() {
      console.log('=== 开始智能返回操作 ===')

      // 获取当前页面栈，用于判断导航状态
      const pages = getCurrentPages()
      console.log('当前页面栈长度:', pages.length)
      console.log('当前页面栈路由:', pages.map(page => page.route))

      // 判断页面栈状态，决定返回策略
      if (pages.length > 1) {
        // 页面栈中有多个页面，说明是正常导航进入的，可以使用 navigateBack 返回
        console.log('检测到正常导航状态，使用 navigateBack 返回上一页')
        uni.navigateBack({
          delta: 1, // 返回上一页
          success: () => {
            console.log('navigateBack 返回成功')
          },
          fail: (err) => {
            console.error('navigateBack 返回失败，可能的原因：页面栈状态异常', err)
            // 如果 navigateBack 失败，使用备用方案
            this.fallbackToHome()
          }
        })
      } else {
        // 页面栈中只有当前页面，说明是刷新后的状态或直接访问
        console.log('检测到页面栈被重置（刷新或直接访问），直接跳转到首页')
        this.fallbackToHome()
      }
    },

    /**
     * 备用返回方案：重新启动应用并跳转到首页
     *
     * 使用场景：
     * 1. 页面栈被重置后的返回操作
     * 2. navigateBack 失败时的降级处理
     *
     * 为什么使用 reLaunch 而不是 navigateTo：
     * - reLaunch 会关闭所有页面并重新启动应用，确保页面栈状态正确
     * - navigateTo 只是简单跳转，可能导致页面栈状态混乱
     */
    fallbackToHome() {
      console.log('=== 执行备用返回方案：重启应用并跳转到首页 ===')
      uni.reLaunch({
        url: '/pages/dashboard/index',
        success: () => {
          console.log('重启应用并跳转到首页成功')
        },
        fail: (err) => {
          console.error('跳转到首页失败，这是一个严重错误:', err)
          // 如果连 reLaunch 都失败了，给用户提示
          uni.showToast({
            title: '返回失败，请重启应用',
            icon: 'none',
            duration: 3000
          })
        }
      })
    },

    /**
     * 状态改变处理
     */
    change(e) {
      console.log('状态改变:', e)
    }
  }
}
</script>

<style lang="scss">
@import url("@/static/scss/dashboard.scss");

.person {
  // 页面头部样式
  .page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 24rpx;
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
    color: #fff;

    .back-button {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 50%;
      transition: background-color 0.2s ease;

      &:active {
        background-color: rgba(255, 255, 255, 0.2);
      }

      .back-icon {
        width: 32rpx;
        height: 32rpx;
        filter: brightness(0) invert(1);
      }
    }

    .page-title {
      flex: 1;
      text-align: center;
      font-size: 36rpx;
      font-weight: 600;
      margin: 0;
    }

    .placeholder {
      width: 60rpx;
      height: 60rpx;
    }
  }

  .box {
    padding: 30rpx 24rpx;
  }
  .in-out-list {
    font-size: 28rpx;
    .item {
      padding: 0 8rpx;
    }
    .row {
      display: grid;
      grid-template-columns: 170rpx auto 120rpx 120rpx 100rpx;
      align-items: center;
      width: 100%;
      .icon {
        width: 34rpx;
        height: 34rpx;
        margin-right: 10rpx;
      }
    }
    .head {
      color: #BBB;
      .name {
        padding-left: 20rpx;
      }
      .row {
        height: 78rpx;
        background: #F9F9F9;
        border-radius: 8rpx;
      }
    }
    .body {
      .row {
        padding: 30rpx 0;
        border-bottom: 1px solid #F7F7F7;
        &:last-child {
          border-bottom: none; 
        }
      }
      .name {
        font-weight: bold;
      }
    }
    .status-tag {
      display: inline-block;
      padding: 0 10rpx;
      line-height: 40rpx;
      border-radius: 6rpx;
      background-size: auto 20rpx;
      &.status-1 {
        background: #FFA257 url("@/static/images/dashboard/tag_bg1.png") no-repeat right bottom;
        color: #FFF;
      }
      &.status-2 {
        background: #EA6055 url("@/static/images/dashboard/tag_bg2.png") no-repeat right bottom;
        color: #FFF;
      }
      &.status-3 {
        background: #2BA97B url("@/static/images/dashboard/tag_bg3.png") no-repeat right bottom;
        color: #FFF;
      }
      &.status-4 {
        background: #EBEBEB url("@/static/images/dashboard/tag_bg4.png") no-repeat right bottom;
        color: #747474;
      }
    }
  }
}
</style>