<template>
  <view class="viewport person">
    <view class="page-container">
      <!-- 页面头部，包含返回按钮和标题 -->
      <view class="page-header">
        <view class="back-button" @click="goBack">
          <image class="back-icon" mode="aspectFit" src="/static/images/icons/close.png" />
        </view>
        <view class="page-title">人员情况</view>
        <view class="placeholder"></view>
      </view>
      <view class="search-box">
        <uni-row :gutter="10">
          <uni-col :span="12">
            <view class="search-item">
              <!-- <image class="icon" mode="aspectFit" src="@/static/images/icons/search.png" /> -->
              <uni-icons class="uni-icon" type="search" color="#BBB" size="22"></uni-icons>
              <view class="search-input">
                <uni-combox :border="false" :candidates="persons" placeholder="人员姓名"></uni-combox>
              </view>
            </view>
          </uni-col>
          <uni-col :span="12">
            <view class="search-item">
              <uni-data-select
                v-model="searchParams.status"
                placeholder="状态"
                :border="false" 
                :localdata="range"
                @change="change"
              ></uni-data-select>
            </view>
          </uni-col>
          <uni-col :span="12">
            <view class="search-item">
              <view class="search-input">
                <uni-datetime-picker
                  :border="false"
                  v-model="searchParams.date"
                  type="daterange"
                  start="2021-3-20"
                  end="2021-5-20"
                  rangeSeparator="至"
                />
              </view>
            </view>
          </uni-col>
          <uni-col :span="12">
            <view class="search-item">
              <uni-data-select
                v-model="searchParams.department"
                placeholder="部门"
                :border="false" 
                :localdata="range"
                @change="change"
              ></uni-data-select>
            </view>
          </uni-col>
        </uni-row>
      </view>
      
      <view class="box">
        <view class="in-out-list">
          <view class="head">
            <view class="row">
              <view class="item name">姓名</view>
              <view class="item dept">部门</view>
              <view class="item in">签到</view>
              <view class="item out">签退</view>
              <view class="item date">日期</view>
            </view>
          </view>
          <view class="body">
            <view class="row">
              <view class="item name">
                <image class="icon" mode="aspectFit" src="@/static/images/icons/person2.png" />
                张三
              </view>
              <view class="item dept ellipsis">销售部</view>
              <view class="item in">
                <view class="status-tag status-1">迟到</view>
              </view>
              <view class="item out">
                <view class="status-tag status-4">已签退</view>
              </view>
              <view class="item date">03-06</view>
            </view>
            <view class="row">
              <view class="item name">
                <image class="icon" mode="aspectFit" src="@/static/images/icons/person2.png" />
                  刘关张三
              </view>
              <view class="item dept ellipsis">销售部销售部销售部</view>
              <view class="item in">
                <view class="status-tag status-1">未签到</view>
              </view>
              <view class="item out">
                <view class="status-tag status-4">已签退</view>
              </view>
              <view class="item date">03-06</view>
            </view>
            <view class="row">
              <view class="item name">
                <image class="icon" mode="aspectFit" src="@/static/images/icons/person2.png" />
                  刘关三
              </view>
              <view class="item dept ellipsis">销售部销售部销售部</view>
              <view class="item in">
                <view class="status-tag status-3">请假</view>
              </view>
              <view class="item out">
                <view class="status-tag status-4">已签退</view>
              </view>
              <view class="item date">03-06</view>
            </view>
            <view class="row">
              <view class="item name">
                <image class="icon" mode="aspectFit" src="@/static/images/icons/person2.png" />
                  刘关三
              </view>
              <view class="item dept ellipsis">销售部销售部销售部</view>
              <view class="item in">
                <view class="status-tag status-4">已签到</view>
              </view>
              <view class="item out">
                <view class="status-tag status-4">已签退</view>
              </view>
              <view class="item date">03-06</view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'PersonPage',
  data() {
    return {
      persons: ['张三', '李四', '王五', '赵六'],
      range: [
        { text: '选项0', value: 0 },
        { text: '选项1', value: 1 },
        { text: '选项2', value: 2 }
      ],
      searchParams: {
        name: '',
        status: undefined,
        date: '',
        department: ''
      }
    }
  },
  methods: {
    /**
     * 返回上一页
     * 智能返回逻辑：优先使用 navigateBack，失败时跳转到首页
     */
    goBack() {
      console.log('开始返回操作')

      // 获取当前页面栈
      const pages = getCurrentPages()
      console.log('当前页面栈长度:', pages.length)
      console.log('当前页面栈:', pages.map(page => page.route))

      // 如果页面栈长度大于1，说明有上一页可以返回
      if (pages.length > 1) {
        console.log('页面栈中有上一页，使用 navigateBack 返回')
        uni.navigateBack({
          delta: 1,
          success: () => {
            console.log('navigateBack 返回成功')
          },
          fail: (err) => {
            console.error('navigateBack 返回失败:', err)
            this.fallbackToHome()
          }
        })
      } else {
        console.log('页面栈中没有上一页，直接跳转到首页')
        this.fallbackToHome()
      }
    },

    /**
     * 备用返回方案：跳转到首页
     */
    fallbackToHome() {
      console.log('使用备用方案跳转到首页')
      uni.reLaunch({
        url: '/pages/dashboard/index',
        success: () => {
          console.log('跳转到首页成功')
        },
        fail: (err) => {
          console.error('跳转到首页失败:', err)
          uni.showToast({
            title: '返回失败，请重试',
            icon: 'none'
          })
        }
      })
    },

    /**
     * 状态改变处理
     */
    change(e) {
      console.log('状态改变:', e)
    }
  }
}
</script>

<style lang="scss">
@import url("@/static/scss/dashboard.scss");

.person {
  // 页面头部样式
  .page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 24rpx;
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
    color: #fff;

    .back-button {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 50%;
      transition: background-color 0.2s ease;

      &:active {
        background-color: rgba(255, 255, 255, 0.2);
      }

      .back-icon {
        width: 32rpx;
        height: 32rpx;
        filter: brightness(0) invert(1);
      }
    }

    .page-title {
      flex: 1;
      text-align: center;
      font-size: 36rpx;
      font-weight: 600;
      margin: 0;
    }

    .placeholder {
      width: 60rpx;
      height: 60rpx;
    }
  }

  .box {
    padding: 30rpx 24rpx;
  }
  .in-out-list {
    font-size: 28rpx;
    .item {
      padding: 0 8rpx;
    }
    .row {
      display: grid;
      grid-template-columns: 170rpx auto 120rpx 120rpx 100rpx;
      align-items: center;
      width: 100%;
      .icon {
        width: 34rpx;
        height: 34rpx;
        margin-right: 10rpx;
      }
    }
    .head {
      color: #BBB;
      .name {
        padding-left: 20rpx;
      }
      .row {
        height: 78rpx;
        background: #F9F9F9;
        border-radius: 8rpx;
      }
    }
    .body {
      .row {
        padding: 30rpx 0;
        border-bottom: 1px solid #F7F7F7;
        &:last-child {
          border-bottom: none; 
        }
      }
      .name {
        font-weight: bold;
      }
    }
    .status-tag {
      display: inline-block;
      padding: 0 10rpx;
      line-height: 40rpx;
      border-radius: 6rpx;
      background-size: auto 20rpx;
      &.status-1 {
        background: #FFA257 url("@/static/images/dashboard/tag_bg1.png") no-repeat right bottom;
        color: #FFF;
      }
      &.status-2 {
        background: #EA6055 url("@/static/images/dashboard/tag_bg2.png") no-repeat right bottom;
        color: #FFF;
      }
      &.status-3 {
        background: #2BA97B url("@/static/images/dashboard/tag_bg3.png") no-repeat right bottom;
        color: #FFF;
      }
      &.status-4 {
        background: #EBEBEB url("@/static/images/dashboard/tag_bg4.png") no-repeat right bottom;
        color: #747474;
      }
    }
  }
}
</style>