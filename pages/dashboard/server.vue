<template>
  <view class="viewport server">
    <view class="page-container">
      <!-- 页面头部，包含返回按钮和标题 -->
      <view class="page-header">
        <view class="back-button" @click="goBack">
          <image class="back-icon" mode="aspectFit" src="/static/images/icons/close.png" />
        </view>
        <view class="page-title">服务器情况</view>
        <view class="placeholder"></view>
      </view>
      <view class="search-box">
        <uni-row :gutter="10">
          <uni-col :span="16">
            <view class="search-item">
              <!-- 搜索图标 -->
              <uni-icons class="uni-icon" type="search" color="#BBB" size="22"></uni-icons>
              <view class="search-input">
                <!-- 将下拉框改为普通输入框，支持服务器编号/名称搜索 -->
                <input
                  v-model="searchParams.serverName"
                  placeholder="请输入服务器编号/服务器名称"
                  placeholder-style="color: #BBB"
                  @input="onServerNameInput"
                  @confirm="onSearchConfirm"
                  confirm-type="search"
                />
              </view>
            </view>
          </uni-col>
          <uni-col :span="8">
            <view class="search-item">
              <uni-data-select
                v-model="searchParams.serverType"
                placeholder="服务器类型"
                :border="false"
                :localdata="serverTypeOptions"
                @change="onServerTypeChange"
              ></uni-data-select>
            </view>
          </uni-col>
          <uni-col :span="24">
            <view class="search-item">
              <view class="search-input">
                <uni-datetime-picker
                  :border="false"
                  v-model="searchParams.dateRange"
                  type="daterange"
                  start="2021-3-20"
                  end="2021-5-20"
                  rangeSeparator="至"
                  @change="onDateRangeChange"
                />
              </view>
            </view>
          </uni-col>
        </uni-row>
      </view>
      
      <!-- 数据加载状态 -->
      <view v-if="serversLoading" class="loading-container">
        <text class="loading-text">正在加载服务器数据...</text>
      </view>

      <!-- 无数据状态 -->
      <view v-else-if="servers.length === 0" class="no-data-container">
        <text class="no-data-text">暂无服务器数据</text>
      </view>

      <!-- 动态渲染服务器列表 -->
      <view v-else>
        <view
          v-for="(server, index) in servers"
          :key="server.serverId || index"
          class="box status-box"
          :class="getServerStatusClass(server)"
        >
          <view class="title">
            <view class="content">
              <view class="circle-icon">
                <image class="image" mode="aspectFit" src="@/static/images/icons/fluent--layer-20-filled.png" />
              </view>
              <text class="text">{{ getServerStatusText(server) }}</text>
            </view>
            <view class="extra">
              <view class="go-detail" @click="goToServerDetail(server)">
                <text class="text">详情</text>
                <uni-icons class="uni-icon" type="right" color="#BBB" size="20"></uni-icons>
              </view>
            </view>
          </view>
          <view class="body">
            <view class="info-list">
              <view class="item">
                <view class="name">
                  <text class="text">检测时间</text>
                </view>
                <view class="value">
                  <text class="text">{{ formatDateTime(server.checkTime || server.updateTime || server.createTime) }}</text>
                </view>
              </view>
              <view class="item">
                <view class="name">
                  <text class="text">服务器编号</text>
                </view>
                <view class="value">
                  <text class="text">{{ server.serverId || server.serverCode || '未知' }}</text>
                </view>
              </view>
              <view class="item">
                <view class="name">
                  <text class="text">服务器名称</text>
                </view>
                <view class="value">
                  <text class="text">{{ server.serverName || server.name || '未命名' }}</text>
                </view>
              </view>
              <view class="item">
                <view class="name">
                  <text class="text">服务器类型</text>
                </view>
                <view class="value">
                  <text class="text">{{ getServerTypeText(server.serverType || server.type) }}</text>
                </view>
              </view>
              <view class="item">
                <view class="name">
                  <text class="text">IP地址</text>
                </view>
                <view class="value">
                  <text class="text">{{ server.ipAddress || server.ip || '未知' }}</text>
                </view>
              </view>
              <view v-if="server.configuration || server.config" class="item multi-line">
                <view class="name">
                  <text class="text">配置</text>
                </view>
                <view class="value">
                  <text class="text">{{ server.configuration || server.config || '配置信息未提供' }}</text>
                </view>
              </view>
              <view v-if="server.description || server.remark" class="item multi-line">
                <view class="name">
                  <text class="text">备注</text>
                </view>
                <view class="value">
                  <text class="text">{{ server.description || server.remark }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getServers } from '@/api/itsm/server'

export default {
  name: 'ServerPage',
  data() {
    return {
      // 服务器数据相关
      servers: [], // 服务器列表数据
      serversLoading: false, // 服务器数据加载状态

      // 搜索参数
      searchParams: {
        serverName: '', // 服务器编号/名称搜索关键词
        serverType: undefined, // 服务器类型
        dateRange: '', // 日期范围
        pageNum: 1, // 当前页码
        pageSize: 10 // 每页数量
      },

      // 服务器类型选项（可根据实际业务调整）
      serverTypeOptions: [
        { text: '全部', value: '' },
        { text: 'Web服务器', value: 'web' },
        { text: '数据库服务器', value: 'database' },
        { text: '应用服务器', value: 'application' },
        { text: '文件服务器', value: 'file' },
        { text: '缓存服务器', value: 'cache' }
      ],

      // 搜索防抖定时器
      searchTimer: null
    }
  },

  // 页面生命周期
  mounted() {
    // 页面加载时获取服务器数据
    this.loadServers()
  },

  methods: {
    /**
     * 加载服务器数据
     * 从后端API获取服务器列表数据
     */
    async loadServers() {
      try {
        this.serversLoading = true
        console.log('=== 开始加载服务器数据 ===')
        console.log('搜索参数:', JSON.stringify(this.searchParams, null, 2))

        // 构建请求参数
        const queryParams = {
          pageNum: this.searchParams.pageNum,
          pageSize: this.searchParams.pageSize
        }

        // 如果有服务器名称搜索条件，添加到请求参数
        if (this.searchParams.serverName && this.searchParams.serverName.trim()) {
          queryParams.serverName = this.searchParams.serverName.trim()
        }

        // 如果有服务器类型筛选条件，添加到请求参数
        if (this.searchParams.serverType) {
          queryParams.serverType = this.searchParams.serverType
        }

        // 如果有日期范围筛选条件，添加到请求参数
        if (this.searchParams.dateRange) {
          queryParams.dateRange = this.searchParams.dateRange
        }

        console.log('最终请求参数:', queryParams)

        // 调用后端API获取服务器数据
        const response = await getServers(queryParams)
        console.log('服务器数据API响应:', JSON.stringify(response, null, 2))

        if (response && response.code === 200 && response.data) {
          // 根据实际返回的数据结构处理服务器数据
          // 从控制台看到 response.data 直接是数组
          let serverList = []

          if (Array.isArray(response.data)) {
            // 如果 data 直接是数组
            serverList = response.data
          } else if (response.data.rows && Array.isArray(response.data.rows)) {
            // 如果 data 是分页对象，包含 rows 数组
            serverList = response.data.rows
          } else if (response.data.list && Array.isArray(response.data.list)) {
            // 如果 data 是对象，包含 list 数组
            serverList = response.data.list
          } else {
            // 其他情况，尝试直接使用 data
            serverList = response.data
          }

          this.servers = serverList || []
          console.log('处理后的服务器数据:', this.servers)
          console.log('服务器数据数量:', this.servers.length)

          // 打印第一个服务器对象的结构，便于调试
          if (this.servers.length > 0) {
            console.log('第一个服务器对象结构:', JSON.stringify(this.servers[0], null, 2))
          }
        } else {
          console.warn('服务器数据格式异常:', response)
          this.servers = []
          uni.showToast({
            title: '服务器数据加载异常',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('加载服务器数据失败:', error)
        this.servers = []
        uni.showToast({
          title: '服务器数据加载失败',
          icon: 'none'
        })
      } finally {
        this.serversLoading = false
        console.log('=== 服务器数据加载完成 ===')
      }
    },

    /**
     * 服务器名称输入处理（带防抖）
     * 用户输入服务器编号/名称时触发搜索
     */
    onServerNameInput(e) {
      const value = e.detail.value
      console.log('服务器名称输入:', value)

      // 更新搜索参数
      this.searchParams.serverName = value

      // 清除之前的定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer)
      }

      // 设置防抖，500ms后执行搜索
      this.searchTimer = setTimeout(() => {
        console.log('执行防抖搜索，关键词:', value)
        this.performSearch()
      }, 500)
    },

    /**
     * 搜索确认处理
     * 用户点击键盘搜索按钮时立即执行搜索
     */
    onSearchConfirm(e) {
      const value = e.detail.value
      console.log('搜索确认，关键词:', value)

      // 清除防抖定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer)
        this.searchTimer = null
      }

      // 立即执行搜索
      this.performSearch()
    },

    /**
     * 服务器类型改变处理
     */
    onServerTypeChange(e) {
      console.log('服务器类型改变:', e)
      this.searchParams.serverType = e
      this.performSearch()
    },

    /**
     * 日期范围改变处理
     */
    onDateRangeChange(e) {
      console.log('日期范围改变:', e)
      this.searchParams.dateRange = e
      this.performSearch()
    },

    /**
     * 执行搜索
     * 重置页码并重新加载数据
     */
    performSearch() {
      console.log('=== 执行搜索操作 ===')
      // 重置到第一页
      this.searchParams.pageNum = 1
      // 重新加载数据
      this.loadServers()
    },

    /**
     * 刷新服务器数据
     * 保持当前搜索条件，重新加载数据
     */
    refreshServers() {
      console.log('刷新服务器数据')
      this.loadServers()
    },

    /**
     * 获取服务器状态样式类
     * 根据服务器状态返回对应的CSS类名
     */
    getServerStatusClass(server) {
      const status = server.status || server.serverStatus || server.state

      // 根据不同的状态值返回对应的样式类
      switch (status) {
        case 'normal':
        case 'online':
        case 'running':
        case '正常':
        case '在线':
        case 1:
          return 'green'
        case 'warning':
        case 'caution':
        case '警告':
        case 2:
          return 'yellow'
        case 'error':
        case 'offline':
        case 'down':
        case '异常':
        case '离线':
        case '故障':
        case 0:
          return 'red'
        case 'maintenance':
        case '维护':
        case 3:
          return 'blue'
        default:
          return 'gray'
      }
    },

    /**
     * 获取服务器状态文本
     * 根据服务器状态返回对应的显示文本
     */
    getServerStatusText(server) {
      const status = server.status || server.serverStatus || server.state

      // 根据不同的状态值返回对应的文本
      switch (status) {
        case 'normal':
        case 'online':
        case 'running':
        case '正常':
        case '在线':
        case 1:
          return '正常'
        case 'warning':
        case 'caution':
        case '警告':
        case 2:
          return '警告'
        case 'error':
        case 'offline':
        case 'down':
        case '异常':
        case '离线':
        case '故障':
        case 0:
          return '异常'
        case 'maintenance':
        case '维护':
        case 3:
          return '维护'
        default:
          return '未知'
      }
    },

    /**
     * 获取服务器类型文本
     * 将服务器类型代码转换为可读文本
     */
    getServerTypeText(type) {
      const typeMap = {
        'web': 'Web服务器',
        'database': '数据库服务器',
        'application': '应用服务器',
        'file': '文件服务器',
        'cache': '缓存服务器',
        'proxy': '代理服务器',
        'mail': '邮件服务器',
        'dns': 'DNS服务器',
        'ftp': 'FTP服务器',
        'game': '游戏服务器'
      }

      return typeMap[type] || type || '未知类型'
    },

    /**
     * 格式化日期时间
     * 将时间戳或日期字符串格式化为可读格式
     */
    formatDateTime(dateTime) {
      if (!dateTime) {
        return '未知时间'
      }

      try {
        let date

        // 处理不同的时间格式
        if (typeof dateTime === 'number') {
          // 时间戳
          date = new Date(dateTime)
        } else if (typeof dateTime === 'string') {
          // 字符串格式
          date = new Date(dateTime)
        } else {
          return '时间格式错误'
        }

        // 检查日期是否有效
        if (isNaN(date.getTime())) {
          return '无效时间'
        }

        // 格式化为 YYYY-MM-DD HH:mm:ss
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')
        const seconds = String(date.getSeconds()).padStart(2, '0')

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
      } catch (error) {
        console.error('日期格式化错误:', error)
        return '时间解析失败'
      }
    },

    /**
     * 跳转到服务器详情页面
     * 点击详情按钮时触发
     */
    goToServerDetail(server) {
      console.log('跳转到服务器详情:', server)

      // 这里可以根据实际需求跳转到详情页面
      // 例如：
      // uni.navigateTo({
      //   url: `/pages/server/detail?serverId=${server.serverId}`
      // })

      // 暂时显示提示
      uni.showToast({
        title: `查看 ${server.serverName || server.serverId} 详情`,
        icon: 'none'
      })
    },
    /**
     * 智能返回上一页功能
     *
     * 问题背景：
     * 当用户从首页菜单进入子页面后，如果在子页面进行刷新操作，会导致页面栈被重置，
     * 此时使用 uni.navigateBack() 会失败，因为页面栈中只有当前页面，没有上一页可以返回。
     *
     * 解决方案：
     * 1. 使用 getCurrentPages() 检测当前页面栈状态
     * 2. 如果页面栈长度 > 1，说明有导航历史，使用 navigateBack 正常返回
     * 3. 如果页面栈长度 = 1，说明是刷新后的状态或直接访问，使用 reLaunch 跳转到首页
     *
     * 页面栈状态说明：
     * - 正常导航：[首页, 当前页面] → 长度为2，可以正常返回
     * - 刷新后：[当前页面] → 长度为1，需要跳转到首页
     * - 直接访问：[当前页面] → 长度为1，需要跳转到首页
     */
    goBack() {
      console.log('=== 开始智能返回操作 ===')

      // 获取当前页面栈，用于判断导航状态
      const pages = getCurrentPages()
      console.log('当前页面栈长度:', pages.length)
      console.log('当前页面栈路由:', pages.map(page => page.route))

      // 判断页面栈状态，决定返回策略
      if (pages.length > 1) {
        // 页面栈中有多个页面，说明是正常导航进入的，可以使用 navigateBack 返回
        console.log('检测到正常导航状态，使用 navigateBack 返回上一页')
        uni.navigateBack({
          delta: 1, // 返回上一页
          success: () => {
            console.log('navigateBack 返回成功')
          },
          fail: (err) => {
            console.error('navigateBack 返回失败，可能的原因：页面栈状态异常', err)
            // 如果 navigateBack 失败，使用备用方案
            this.fallbackToHome()
          }
        })
      } else {
        // 页面栈中只有当前页面，说明是刷新后的状态或直接访问
        console.log('检测到页面栈被重置（刷新或直接访问），直接跳转到首页')
        this.fallbackToHome()
      }
    },

    /**
     * 备用返回方案：重新启动应用并跳转到首页
     *
     * 使用场景：
     * 1. 页面栈被重置后的返回操作
     * 2. navigateBack 失败时的降级处理
     *
     * 为什么使用 reLaunch 而不是 navigateTo：
     * - reLaunch 会关闭所有页面并重新启动应用，确保页面栈状态正确
     * - navigateTo 只是简单跳转，可能导致页面栈状态混乱
     */
    fallbackToHome() {
      console.log('=== 执行备用返回方案：重启应用并跳转到首页 ===')
      uni.reLaunch({
        url: '/pages/dashboard/index',
        success: () => {
          console.log('重启应用并跳转到首页成功')
        },
        fail: (err) => {
          console.error('跳转到首页失败，这是一个严重错误:', err)
          // 如果连 reLaunch 都失败了，给用户提示
          uni.showToast({
            title: '返回失败，请重启应用',
            icon: 'none',
            duration: 3000
          })
        }
      })
    },

    /**
     * 通用状态改变处理（保留原有方法，用于兼容）
     */
    change(e) {
      console.log('通用状态改变:', e)
    }
  },

  // 页面销毁时清理定时器
  beforeDestroy() {
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
      this.searchTimer = null
    }
  }
}
</script>

<style lang="scss">
@import url("@/static/scss/dashboard.scss");

.server {
  // 页面头部样式
  .page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 24rpx;
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
    color: #fff;

    .back-button {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 50%;
      transition: background-color 0.2s ease;

      &:active {
        background-color: rgba(255, 255, 255, 0.2);
      }

      .back-icon {
        width: 32rpx;
        height: 32rpx;
        filter: brightness(0) invert(1);
      }
    }

    .page-title {
      flex: 1;
      text-align: center;
      font-size: 36rpx;
      font-weight: 600;
      margin: 0;
    }

    .placeholder {
      width: 60rpx;
      height: 60rpx;
    }
  }

  // 搜索框样式
  .search-box {
    .search-item {
      .search-input {
        input {
          width: 100%;
          height: 100%;
          font-size: 28rpx;
          color: #333;
          background: transparent;
          border: none;
          outline: none;

          // 确保输入框样式与原有组件一致
          &::placeholder {
            color: #BBB;
            font-size: 28rpx;
          }
        }
      }
    }
  }

  // 加载状态样式
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 80rpx 0;

    .loading-text {
      color: #999;
      font-size: 28rpx;
    }
  }

  // 无数据状态样式
  .no-data-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 120rpx 0;

    .no-data-text {
      color: #999;
      font-size: 28rpx;
    }
  }

  // 服务器状态样式扩展
  .status-box {
    // 黄色警告状态
    &.yellow {
      .title .content .text {
        color: #F39C12;
      }

      .circle-icon .image {
        filter: hue-rotate(45deg);
      }
    }

    // 蓝色维护状态
    &.blue {
      .title .content .text {
        color: #3498DB;
      }

      .circle-icon .image {
        filter: hue-rotate(200deg);
      }
    }

    // 灰色未知状态
    &.gray {
      .title .content .text {
        color: #95A5A6;
      }

      .circle-icon .image {
        filter: grayscale(100%);
      }
    }
  }
}
</style>