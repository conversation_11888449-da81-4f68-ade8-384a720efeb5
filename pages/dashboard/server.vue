<template>
  <view class="viewport server">
    <view class="page-container">
      <!-- 页面头部，包含返回按钮和标题 -->
      <view class="page-header">
        <view class="back-button" @click="goBack">
          <image class="back-icon" mode="aspectFit" src="/static/images/icons/close.png" />
        </view>
        <view class="page-title">服务器情况</view>
        <view class="placeholder"></view>
      </view>
      <view class="search-box">
        <uni-row :gutter="10">
          <uni-col :span="16">
            <view class="search-item">
              <!-- <image class="icon" mode="aspectFit" src="@/static/images/icons/search.png" /> -->
              <uni-icons class="uni-icon" type="search" color="#BBB" size="22"></uni-icons>
              <view class="search-input">
                <uni-combox :border="false" :candidates="persons" placeholder="服务器编号/服务器名称"></uni-combox>
              </view>
            </view>
          </uni-col>
          <uni-col :span="8">
            <view class="search-item">
              <uni-data-select
                v-model="searchParams.status"
                placeholder="状态"
                :border="false" 
                :localdata="range"
                @change="change"
              ></uni-data-select>
            </view>
          </uni-col>
          <uni-col :span="24">
            <view class="search-item">
              <view class="search-input">
                <uni-datetime-picker
                  :border="false"
                  v-model="searchParams.date"
                  type="daterange"
                  start="2021-3-20"
                  end="2021-5-20"
                  rangeSeparator="至"
                />
              </view>
            </view>
          </uni-col>
        </uni-row>
      </view>
      
      <view class="box status-box green">
        <view class="title">
          <view class="content">
            <view class="circle-icon">
              <image class="image" mode="aspectFit" src="@/static/images/icons/fluent--layer-20-filled.png" />
            </view>
            <text class="text">正常</text>
          </view>
          <view class="extra">
            <view class="go-detail">
              <text class="text">详情</text>
              <uni-icons class="uni-icon" type="right" color="#BBB" size="20"></uni-icons>
            </view>
          </view>
        </view>
        <view class="body">
          <view class="info-list">
            <view class="item">
              <view class="name">
                <text class="text">检测时间</text>
              </view>
              <view class="value">
                <text class="text">2025-3-21 10:10:32</text>
              </view>
            </view>
            <view class="item">
              <view class="name">
                <text class="text">服务器编号</text>
              </view>
              <view class="value">
                <text class="text">ZSJ0001</text>
              </view>
            </view>
            <view class="item">
              <view class="name">
                <text class="text">服务器名称</text>
              </view>
              <view class="value">
                <text class="text">XXX系统</text>
              </view>
            </view>
            <view class="item">
              <view class="name">
                <text class="text">部门/处理人</text>
              </view>
              <view class="value">
                <text class="text">京信股份  张三</text>
              </view>
            </view>
            <view class="item multi-line">
              <view class="name">
                <text class="text">配置</text>
              </view>
              <view class="value">
                <text class="text">CPU：8核；内存：16GB；存储: 500GB；带宽：100MB。</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view class="box status-box red">
        <view class="title">
          <view class="content">
            <view class="circle-icon">
              <image class="image" mode="aspectFit" src="@/static/images/icons/fluent--layer-20-filled.png" />
            </view>
            <text class="text">正常</text>
          </view>
          <view class="extra">
            <view class="go-detail">
              <text class="text">详情</text>
              <uni-icons class="uni-icon" type="right" color="#BBB" size="20"></uni-icons>
            </view>
          </view>
        </view>
        <view class="body">
          <view class="info-list">
            <view class="item">
              <view class="name">
                <text class="text">检测时间</text>
              </view>
              <view class="value">
                <text class="text">2025-3-21 10:10:32</text>
              </view>
            </view>
            <view class="item">
              <view class="name">
                <text class="text">系统编号</text>
              </view>
              <view class="value">
                <text class="text">ZSJ0001</text>
              </view>
            </view>
            <view class="item">
              <view class="name">
                <text class="text">系统名称</text>
              </view>
              <view class="value">
                <text class="text">XXX系统</text>
              </view>
            </view>
            <view class="item">
              <view class="name">
                <text class="text">部门/处理人</text>
              </view>
              <view class="value">
                <text class="text">京信股份  张三</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ServerPage',
  data() {
    return {
      persons: ['张三', '李四', '王五', '赵六'],
      range: [
        { text: '选项0', value: 0 },
        { text: '选项1', value: 1 },
        { text: '选项2', value: 2 }
      ],
      searchParams: {
        name: '',
        status: undefined,
        date: '',
        department: ''
      }
    }
  },
  methods: {
    /**
     * 返回上一页
     * 智能返回逻辑：优先使用 navigateBack，失败时跳转到首页
     */
    goBack() {
      console.log('开始返回操作')

      // 获取当前页面栈
      const pages = getCurrentPages()
      console.log('当前页面栈长度:', pages.length)
      console.log('当前页面栈:', pages.map(page => page.route))

      // 如果页面栈长度大于1，说明有上一页可以返回
      if (pages.length > 1) {
        console.log('页面栈中有上一页，使用 navigateBack 返回')
        uni.navigateBack({
          delta: 1,
          success: () => {
            console.log('navigateBack 返回成功')
          },
          fail: (err) => {
            console.error('navigateBack 返回失败:', err)
            this.fallbackToHome()
          }
        })
      } else {
        console.log('页面栈中没有上一页，直接跳转到首页')
        this.fallbackToHome()
      }
    },

    /**
     * 备用返回方案：跳转到首页
     */
    fallbackToHome() {
      console.log('使用备用方案跳转到首页')
      uni.reLaunch({
        url: '/pages/dashboard/index',
        success: () => {
          console.log('跳转到首页成功')
        },
        fail: (err) => {
          console.error('跳转到首页失败:', err)
          uni.showToast({
            title: '返回失败，请重试',
            icon: 'none'
          })
        }
      })
    },

    /**
     * 状态改变处理
     */
    change(e) {
      console.log('状态改变:', e)
    }
  }
}
</script>

<style lang="scss">
@import url("@/static/scss/dashboard.scss");

.server {
  // 页面头部样式
  .page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 24rpx;
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
    color: #fff;

    .back-button {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 50%;
      transition: background-color 0.2s ease;

      &:active {
        background-color: rgba(255, 255, 255, 0.2);
      }

      .back-icon {
        width: 32rpx;
        height: 32rpx;
        filter: brightness(0) invert(1);
      }
    }

    .page-title {
      flex: 1;
      text-align: center;
      font-size: 36rpx;
      font-weight: 600;
      margin: 0;
    }

    .placeholder {
      width: 60rpx;
      height: 60rpx;
    }
  }
}
</style>