<template>
  <view class="viewport server">
    <view class="page-container">
      <!-- 页面头部，包含返回按钮和标题 -->
      <view class="page-header">
        <view class="back-button" @click="goBack">
          <image class="back-icon" mode="aspectFit" src="/static/images/icons/close.png" />
        </view>
        <view class="page-title">服务器情况</view>
        <view class="placeholder"></view>
      </view>
      <view class="search-box">
        <uni-row :gutter="10">
          <uni-col :span="16">
            <view class="search-item">
              <!-- 搜索图标 -->
              <uni-icons class="uni-icon" type="search" color="#BBB" size="22"></uni-icons>
              <view class="search-input">
                <!-- 将下拉框改为普通输入框，支持服务器编号/名称搜索 -->
                <input
                  v-model="searchParams.serverName"
                  placeholder="请输入服务器编号/服务器名称"
                  placeholder-style="color: #BBB"
                  @input="onServerNameInput"
                  @confirm="onSearchConfirm"
                  confirm-type="search"
                />
              </view>
            </view>
          </uni-col>
          <uni-col :span="8">
            <view class="search-item">
              <uni-data-select
                v-model="searchParams.serverType"
                placeholder="服务器类型"
                :border="false"
                :localdata="serverTypeOptions"
                @change="onServerTypeChange"
              ></uni-data-select>
            </view>
          </uni-col>
          <uni-col :span="24">
            <view class="search-item">
              <view class="search-input">
                <uni-datetime-picker
                  :border="false"
                  v-model="searchParams.dateRange"
                  type="daterange"
                  start="2021-3-20"
                  end="2021-5-20"
                  rangeSeparator="至"
                  @change="onDateRangeChange"
                />
              </view>
            </view>
          </uni-col>
        </uni-row>
      </view>
      
      <view class="box status-box green">
        <view class="title">
          <view class="content">
            <view class="circle-icon">
              <image class="image" mode="aspectFit" src="@/static/images/icons/fluent--layer-20-filled.png" />
            </view>
            <text class="text">正常</text>
          </view>
          <view class="extra">
            <view class="go-detail">
              <text class="text">详情</text>
              <uni-icons class="uni-icon" type="right" color="#BBB" size="20"></uni-icons>
            </view>
          </view>
        </view>
        <view class="body">
          <view class="info-list">
            <view class="item">
              <view class="name">
                <text class="text">检测时间</text>
              </view>
              <view class="value">
                <text class="text">2025-3-21 10:10:32</text>
              </view>
            </view>
            <view class="item">
              <view class="name">
                <text class="text">服务器编号</text>
              </view>
              <view class="value">
                <text class="text">ZSJ0001</text>
              </view>
            </view>
            <view class="item">
              <view class="name">
                <text class="text">服务器名称</text>
              </view>
              <view class="value">
                <text class="text">XXX系统</text>
              </view>
            </view>
            <view class="item">
              <view class="name">
                <text class="text">部门/处理人</text>
              </view>
              <view class="value">
                <text class="text">京信股份  张三</text>
              </view>
            </view>
            <view class="item multi-line">
              <view class="name">
                <text class="text">配置</text>
              </view>
              <view class="value">
                <text class="text">CPU：8核；内存：16GB；存储: 500GB；带宽：100MB。</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view class="box status-box red">
        <view class="title">
          <view class="content">
            <view class="circle-icon">
              <image class="image" mode="aspectFit" src="@/static/images/icons/fluent--layer-20-filled.png" />
            </view>
            <text class="text">正常</text>
          </view>
          <view class="extra">
            <view class="go-detail">
              <text class="text">详情</text>
              <uni-icons class="uni-icon" type="right" color="#BBB" size="20"></uni-icons>
            </view>
          </view>
        </view>
        <view class="body">
          <view class="info-list">
            <view class="item">
              <view class="name">
                <text class="text">检测时间</text>
              </view>
              <view class="value">
                <text class="text">2025-3-21 10:10:32</text>
              </view>
            </view>
            <view class="item">
              <view class="name">
                <text class="text">系统编号</text>
              </view>
              <view class="value">
                <text class="text">ZSJ0001</text>
              </view>
            </view>
            <view class="item">
              <view class="name">
                <text class="text">系统名称</text>
              </view>
              <view class="value">
                <text class="text">XXX系统</text>
              </view>
            </view>
            <view class="item">
              <view class="name">
                <text class="text">部门/处理人</text>
              </view>
              <view class="value">
                <text class="text">京信股份  张三</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ServerPage',
  data() {
    return {
      persons: ['张三', '李四', '王五', '赵六'],
      range: [
        { text: '选项0', value: 0 },
        { text: '选项1', value: 1 },
        { text: '选项2', value: 2 }
      ],
      searchParams: {
        name: '',
        status: undefined,
        date: '',
        department: ''
      }
    }
  },
  methods: {
    /**
     * 智能返回上一页功能
     *
     * 问题背景：
     * 当用户从首页菜单进入子页面后，如果在子页面进行刷新操作，会导致页面栈被重置，
     * 此时使用 uni.navigateBack() 会失败，因为页面栈中只有当前页面，没有上一页可以返回。
     *
     * 解决方案：
     * 1. 使用 getCurrentPages() 检测当前页面栈状态
     * 2. 如果页面栈长度 > 1，说明有导航历史，使用 navigateBack 正常返回
     * 3. 如果页面栈长度 = 1，说明是刷新后的状态或直接访问，使用 reLaunch 跳转到首页
     *
     * 页面栈状态说明：
     * - 正常导航：[首页, 当前页面] → 长度为2，可以正常返回
     * - 刷新后：[当前页面] → 长度为1，需要跳转到首页
     * - 直接访问：[当前页面] → 长度为1，需要跳转到首页
     */
    goBack() {
      console.log('=== 开始智能返回操作 ===')

      // 获取当前页面栈，用于判断导航状态
      const pages = getCurrentPages()
      console.log('当前页面栈长度:', pages.length)
      console.log('当前页面栈路由:', pages.map(page => page.route))

      // 判断页面栈状态，决定返回策略
      if (pages.length > 1) {
        // 页面栈中有多个页面，说明是正常导航进入的，可以使用 navigateBack 返回
        console.log('检测到正常导航状态，使用 navigateBack 返回上一页')
        uni.navigateBack({
          delta: 1, // 返回上一页
          success: () => {
            console.log('navigateBack 返回成功')
          },
          fail: (err) => {
            console.error('navigateBack 返回失败，可能的原因：页面栈状态异常', err)
            // 如果 navigateBack 失败，使用备用方案
            this.fallbackToHome()
          }
        })
      } else {
        // 页面栈中只有当前页面，说明是刷新后的状态或直接访问
        console.log('检测到页面栈被重置（刷新或直接访问），直接跳转到首页')
        this.fallbackToHome()
      }
    },

    /**
     * 备用返回方案：重新启动应用并跳转到首页
     *
     * 使用场景：
     * 1. 页面栈被重置后的返回操作
     * 2. navigateBack 失败时的降级处理
     *
     * 为什么使用 reLaunch 而不是 navigateTo：
     * - reLaunch 会关闭所有页面并重新启动应用，确保页面栈状态正确
     * - navigateTo 只是简单跳转，可能导致页面栈状态混乱
     */
    fallbackToHome() {
      console.log('=== 执行备用返回方案：重启应用并跳转到首页 ===')
      uni.reLaunch({
        url: '/pages/dashboard/index',
        success: () => {
          console.log('重启应用并跳转到首页成功')
        },
        fail: (err) => {
          console.error('跳转到首页失败，这是一个严重错误:', err)
          // 如果连 reLaunch 都失败了，给用户提示
          uni.showToast({
            title: '返回失败，请重启应用',
            icon: 'none',
            duration: 3000
          })
        }
      })
    },

    /**
     * 状态改变处理
     */
    change(e) {
      console.log('状态改变:', e)
    }
  }
}
</script>

<style lang="scss">
@import url("@/static/scss/dashboard.scss");

.server {
  // 页面头部样式
  .page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 24rpx;
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
    color: #fff;

    .back-button {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 50%;
      transition: background-color 0.2s ease;

      &:active {
        background-color: rgba(255, 255, 255, 0.2);
      }

      .back-icon {
        width: 32rpx;
        height: 32rpx;
        filter: brightness(0) invert(1);
      }
    }

    .page-title {
      flex: 1;
      text-align: center;
      font-size: 36rpx;
      font-weight: 600;
      margin: 0;
    }

    .placeholder {
      width: 60rpx;
      height: 60rpx;
    }
  }
}
</style>