/**
 * 响应式设计全局混入
 * 为所有组件提供响应式设计相关的方法和数据
 */

import deviceDetector from '@/utils/device'

export default {
  data() {
    return {
      // 设备信息
      $device: {
        type: '',
        screenSize: '',
        systemInfo: null,
        safeArea: null
      }
    }
  },
  
  computed: {
    // 是否为手机
    $isPhone() {
      return deviceDetector.isPhone()
    },
    
    // 是否为平板
    $isTablet() {
      return deviceDetector.isTablet()
    },
    
    // 是否为桌面端
    $isDesktop() {
      return deviceDetector.isDesktop()
    },
    
    // 是否为小屏设备
    $isSmallScreen() {
      return deviceDetector.isSmallScreen()
    },
    
    // 是否为大屏设备
    $isLargeScreen() {
      return deviceDetector.isLargeScreen()
    },
    
    // 响应式配置
    $responsiveConfig() {
      return deviceDetector.getResponsiveConfig()
    },
    
    // 安全区域信息
    $safeArea() {
      return deviceDetector.getSafeArea()
    },
    
    // 导航栏高度
    $navigationBarHeight() {
      return deviceDetector.getNavigationBarHeight()
    }
  },
  
  created() {
    this.$initResponsive()
  },
  
  methods: {
    /**
     * 初始化响应式数据
     */
    $initResponsive() {
      this.$device.type = deviceDetector.getDeviceType()
      this.$device.screenSize = deviceDetector.getScreenSize()
      this.$device.systemInfo = deviceDetector.getSystemInfo()
      this.$device.safeArea = deviceDetector.getSafeArea()
      
      // 监听屏幕变化
      deviceDetector.onScreenChange((deviceType, screenSize) => {
        this.$device.type = deviceType
        this.$device.screenSize = screenSize
        this.$onScreenChange && this.$onScreenChange(deviceType, screenSize)
      })
    },
    
    /**
     * rpx转px
     */
    $rpxToPx(rpx) {
      return deviceDetector.rpxToPx(rpx)
    },
    
    /**
     * px转rpx
     */
    $pxToRpx(px) {
      return deviceDetector.pxToRpx(px)
    },
    
    /**
     * 获取适配后的尺寸
     */
    $getAdaptiveSize(baseSize, type = 'width') {
      return deviceDetector.getAdaptiveSize(baseSize, type)
    },
    
    /**
     * 获取网格列数
     */
    $getGridColumns(defaultCols = 2) {
      return deviceDetector.getGridColumns(defaultCols)
    },
    
    /**
     * 获取卡片间距
     */
    $getCardSpacing() {
      return deviceDetector.getCardSpacing()
    },
    
    /**
     * 获取字体大小
     */
    $getFontSize(size = 'base') {
      return deviceDetector.getFontSize(size)
    },
    
    /**
     * 获取内边距
     */
    $getPadding(size = 'base') {
      return deviceDetector.getPadding(size)
    },
    
    /**
     * 根据设备类型获取样式
     */
    $getDeviceStyle(styles) {
      const deviceType = this.$device.type
      if (styles[deviceType]) {
        return styles[deviceType]
      }
      return styles.default || {}
    },
    
    /**
     * 根据屏幕尺寸获取样式
     */
    $getScreenStyle(styles) {
      const screenSize = this.$device.screenSize
      if (styles[screenSize]) {
        return styles[screenSize]
      }
      return styles.default || {}
    },
    
    /**
     * 获取响应式类名
     */
    $getResponsiveClass(baseClass, suffix = '') {
      const deviceType = this.$device.type
      const screenSize = this.$device.screenSize
      
      const classes = [baseClass]
      
      if (suffix) {
        classes.push(`${baseClass}-${suffix}`)
      }
      
      classes.push(`${baseClass}-${deviceType}`)
      classes.push(`${baseClass}-${screenSize}`)
      
      return classes.join(' ')
    },
    
    /**
     * 设置页面标题（响应式）
     */
    $setResponsiveTitle(title) {
      const maxLength = this.$isSmallScreen ? 10 : 20
      const displayTitle = title.length > maxLength ? title.substring(0, maxLength) + '...' : title
      
      uni.setNavigationBarTitle({
        title: displayTitle
      })
    },
    
    /**
     * 显示响应式Toast
     */
    $showResponsiveToast(message, options = {}) {
      const maxLength = this.$isSmallScreen ? 20 : 40
      const displayMessage = message.length > maxLength ? message.substring(0, maxLength) + '...' : message
      
      uni.showToast({
        title: displayMessage,
        duration: this.$isSmallScreen ? 2000 : 1500,
        ...options
      })
    },
    
    /**
     * 响应式导航
     */
    $navigateResponsive(url, options = {}) {
      // 在小屏设备上使用redirectTo，大屏设备上使用navigateTo
      const navigateMethod = this.$isSmallScreen ? 'redirectTo' : 'navigateTo'
      
      uni[navigateMethod]({
        url,
        ...options
      })
    },
    
    /**
     * 获取响应式图片尺寸
     */
    $getImageSize(baseWidth, baseHeight) {
      const multiplier = {
        'small-phone': 0.8,
        'phone': 1,
        'large-phone': 1.1,
        'tablet': 1.3,
        'desktop': 1.5
      }
      
      const factor = multiplier[this.$device.type] || 1
      
      return {
        width: Math.round(baseWidth * factor),
        height: Math.round(baseHeight * factor)
      }
    },
    
    /**
     * 获取响应式弹窗配置
     */
    $getModalConfig() {
      const configs = {
        'small-phone': {
          width: '90%',
          maxHeight: '80%',
          borderRadius: '8rpx'
        },
        'phone': {
          width: '85%',
          maxHeight: '75%',
          borderRadius: '10rpx'
        },
        'large-phone': {
          width: '80%',
          maxHeight: '70%',
          borderRadius: '12rpx'
        },
        'tablet': {
          width: '60%',
          maxHeight: '65%',
          borderRadius: '16rpx'
        },
        'desktop': {
          width: '40%',
          maxHeight: '60%',
          borderRadius: '20rpx'
        }
      }
      
      return configs[this.$device.type] || configs['phone']
    },
    
    /**
     * 获取响应式列表配置
     */
    $getListConfig() {
      const configs = {
        'small-phone': {
          itemHeight: '120rpx',
          fontSize: '28rpx',
          padding: '20rpx'
        },
        'phone': {
          itemHeight: '140rpx',
          fontSize: '30rpx',
          padding: '24rpx'
        },
        'large-phone': {
          itemHeight: '160rpx',
          fontSize: '32rpx',
          padding: '28rpx'
        },
        'tablet': {
          itemHeight: '180rpx',
          fontSize: '36rpx',
          padding: '32rpx'
        },
        'desktop': {
          itemHeight: '200rpx',
          fontSize: '40rpx',
          padding: '40rpx'
        }
      }
      
      return configs[this.$device.type] || configs['phone']
    }
  }
}
