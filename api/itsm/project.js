import request from '@/utils/request'

// 分页查询
export function getProjects(query) {
    return request({
        url: '/itsm/projectInfo/list',
        headers: {
            isToken: true,
        },
        method: 'get',
        params: query
    })
}

// 获取当前系统所有项目及其下所属服务器，系统，人员
export function getAllProjects(query) {
    return request({
        url: '/itsm/monitorData/getItsmAllProjectInfo',
        headers: {
            isToken: false,
        },
        method: 'get',
        params: query
    })
}

// 获取项目信息详细信息
export function getProjectInfo(projectId) {
    return request({
        url: `/itsm/monitorData/${projectId}`,
        headers: {
            isToken: false,
        },
        method: 'get'
    })
}

// 获取人员情况，如果有传项目id则根据项目id查询，没有则统计全部项目合计总人数
export function getProjectUserCountInfo(projectId) {
    return request({
        url: '/itsm/monitorData/getProjectUserCountInfo',
        headers: {
            isToken: false,
        },
        method: 'get',
        params: projectId ? { projectId } : {}
    })
}