/**
 * 设备检测和响应式工具类
 * 用于检测不同设备类型并提供相应的适配方案
 */

class DeviceDetector {
  constructor() {
    this.systemInfo = null
    this.deviceType = null
    this.screenSize = null
    this.init()
  }

  /**
   * 初始化设备信息
   */
  init() {
    try {
      this.systemInfo = uni.getSystemInfoSync()
      this.detectDeviceType()
      this.detectScreenSize()
    } catch (error) {
      console.error('设备信息获取失败:', error)
    }
  }

  /**
   * 检测设备类型
   */
  detectDeviceType() {
    if (!this.systemInfo) return

    const { platform, screenWidth, screenHeight } = this.systemInfo
    const minDimension = Math.min(screenWidth, screenHeight)
    const maxDimension = Math.max(screenWidth, screenHeight)
    const aspectRatio = maxDimension / minDimension

    // 基于屏幕尺寸和平台判断设备类型
    if (platform === 'ios' || platform === 'android') {
      if (minDimension >= 768) {
        this.deviceType = 'tablet'
      } else if (minDimension >= 414) {
        this.deviceType = 'large-phone'
      } else if (minDimension >= 375) {
        this.deviceType = 'phone'
      } else {
        this.deviceType = 'small-phone'
      }
    } else {
      // H5环境
      if (minDimension >= 1024) {
        this.deviceType = 'desktop'
      } else if (minDimension >= 768) {
        this.deviceType = 'tablet'
      } else if (minDimension >= 414) {
        this.deviceType = 'large-phone'
      } else if (minDimension >= 375) {
        this.deviceType = 'phone'
      } else {
        this.deviceType = 'small-phone'
      }
    }
  }

  /**
   * 检测屏幕尺寸分类
   */
  detectScreenSize() {
    if (!this.systemInfo) return

    const { screenWidth } = this.systemInfo

    if (screenWidth < 375) {
      this.screenSize = 'xs'
    } else if (screenWidth < 414) {
      this.screenSize = 'sm'
    } else if (screenWidth < 768) {
      this.screenSize = 'md'
    } else if (screenWidth < 1024) {
      this.screenSize = 'lg'
    } else {
      this.screenSize = 'xl'
    }
  }

  /**
   * 获取设备类型
   */
  getDeviceType() {
    return this.deviceType
  }

  /**
   * 获取屏幕尺寸分类
   */
  getScreenSize() {
    return this.screenSize
  }

  /**
   * 获取系统信息
   */
  getSystemInfo() {
    return this.systemInfo
  }

  /**
   * 是否为手机
   */
  isPhone() {
    return ['small-phone', 'phone', 'large-phone'].includes(this.deviceType)
  }

  /**
   * 是否为平板
   */
  isTablet() {
    return this.deviceType === 'tablet'
  }

  /**
   * 是否为桌面端
   */
  isDesktop() {
    return this.deviceType === 'desktop'
  }

  /**
   * 是否为小屏设备
   */
  isSmallScreen() {
    return ['xs', 'sm'].includes(this.screenSize)
  }

  /**
   * 是否为大屏设备
   */
  isLargeScreen() {
    return ['lg', 'xl'].includes(this.screenSize)
  }

  /**
   * 获取响应式配置
   */
  getResponsiveConfig() {
    const configs = {
      'small-phone': {
        gridCols: 2,
        cardSpacing: '16rpx',
        fontSize: {
          small: '24rpx',
          base: '28rpx',
          large: '32rpx',
          xl: '36rpx'
        },
        padding: {
          small: '16rpx',
          base: '24rpx',
          large: '32rpx'
        }
      },
      'phone': {
        gridCols: 2,
        cardSpacing: '20rpx',
        fontSize: {
          small: '26rpx',
          base: '30rpx',
          large: '34rpx',
          xl: '38rpx'
        },
        padding: {
          small: '20rpx',
          base: '30rpx',
          large: '40rpx'
        }
      },
      'large-phone': {
        gridCols: 3,
        cardSpacing: '24rpx',
        fontSize: {
          small: '28rpx',
          base: '32rpx',
          large: '36rpx',
          xl: '40rpx'
        },
        padding: {
          small: '24rpx',
          base: '36rpx',
          large: '48rpx'
        }
      },
      'tablet': {
        gridCols: 4,
        cardSpacing: '32rpx',
        fontSize: {
          small: '32rpx',
          base: '36rpx',
          large: '40rpx',
          xl: '44rpx'
        },
        padding: {
          small: '32rpx',
          base: '48rpx',
          large: '64rpx'
        }
      },
      'desktop': {
        gridCols: 6,
        cardSpacing: '40rpx',
        fontSize: {
          small: '36rpx',
          base: '40rpx',
          large: '44rpx',
          xl: '48rpx'
        },
        padding: {
          small: '40rpx',
          base: '60rpx',
          large: '80rpx'
        }
      }
    }

    return configs[this.deviceType] || configs['phone']
  }

  /**
   * 获取安全区域信息
   */
  getSafeArea() {
    if (!this.systemInfo) return {}

    const { safeArea, safeAreaInsets, statusBarHeight, screenHeight, windowHeight } = this.systemInfo

    return {
      safeArea: safeArea || {},
      safeAreaInsets: safeAreaInsets || {},
      statusBarHeight: statusBarHeight || 0,
      bottomSafeHeight: safeAreaInsets ? safeAreaInsets.bottom : 0,
      screenHeight,
      windowHeight
    }
  }

  /**
   * 获取导航栏高度
   */
  getNavigationBarHeight() {
    // #ifdef MP-WEIXIN
    const menuButton = uni.getMenuButtonBoundingClientRect()
    return menuButton.top + menuButton.height + (menuButton.top - this.systemInfo.statusBarHeight)
    // #endif

    // #ifdef APP-PLUS
    return this.systemInfo.statusBarHeight + 44
    // #endif

    // #ifdef H5
    return 44
    // #endif

    return 44
  }

  /**
   * 计算rpx转px
   */
  rpxToPx(rpx) {
    if (!this.systemInfo) return rpx
    return (rpx / 750) * this.systemInfo.screenWidth
  }

  /**
   * 计算px转rpx
   */
  pxToRpx(px) {
    if (!this.systemInfo) return px
    return (px / this.systemInfo.screenWidth) * 750
  }

  /**
   * 获取适配后的尺寸
   */
  getAdaptiveSize(baseSize, type = 'width') {
    const config = this.getResponsiveConfig()
    const multiplier = {
      'small-phone': 0.8,
      'phone': 1,
      'large-phone': 1.1,
      'tablet': 1.3,
      'desktop': 1.5
    }

    return baseSize * (multiplier[this.deviceType] || 1)
  }

  /**
   * 获取网格列数
   */
  getGridColumns(defaultCols = 2) {
    const config = this.getResponsiveConfig()
    return config.gridCols || defaultCols
  }

  /**
   * 获取卡片间距
   */
  getCardSpacing() {
    const config = this.getResponsiveConfig()
    return config.cardSpacing || '24rpx'
  }

  /**
   * 获取字体大小
   */
  getFontSize(size = 'base') {
    const config = this.getResponsiveConfig()
    return config.fontSize[size] || config.fontSize.base
  }

  /**
   * 获取内边距
   */
  getPadding(size = 'base') {
    const config = this.getResponsiveConfig()
    return config.padding[size] || config.padding.base
  }

  /**
   * 监听屏幕变化
   */
  onScreenChange(callback) {
    // #ifdef H5
    window.addEventListener('resize', () => {
      this.init()
      callback && callback(this.getDeviceType(), this.getScreenSize())
    })
    // #endif
  }
}

// 创建全局实例
const deviceDetector = new DeviceDetector()

export default deviceDetector

// 导出常用方法
export const {
  getDeviceType,
  getScreenSize,
  isPhone,
  isTablet,
  isDesktop,
  isSmallScreen,
  isLargeScreen,
  getResponsiveConfig,
  getSafeArea,
  getNavigationBarHeight,
  rpxToPx,
  pxToRpx,
  getAdaptiveSize,
  getGridColumns,
  getCardSpacing,
  getFontSize,
  getPadding,
  onScreenChange
} = deviceDetector
