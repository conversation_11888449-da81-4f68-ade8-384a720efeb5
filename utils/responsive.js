/**
 * 简单的响应式工具函数
 * 不依赖复杂的框架，安全可靠
 */

/**
 * 获取设备信息
 */
export function getDeviceInfo() {
  try {
    const systemInfo = uni.getSystemInfoSync()
    return {
      screenWidth: systemInfo.screenWidth,
      screenHeight: systemInfo.screenHeight,
      windowWidth: systemInfo.windowWidth,
      windowHeight: systemInfo.windowHeight,
      platform: systemInfo.platform,
      statusBarHeight: systemInfo.statusBarHeight,
      safeArea: systemInfo.safeArea || {},
      safeAreaInsets: systemInfo.safeAreaInsets || {}
    }
  } catch (error) {
    console.error('获取设备信息失败:', error)
    return {
      screenWidth: 375,
      screenHeight: 667,
      windowWidth: 375,
      windowHeight: 667,
      platform: 'unknown',
      statusBarHeight: 20,
      safeArea: {},
      safeAreaInsets: {}
    }
  }
}

/**
 * 判断设备类型
 */
export function getDeviceType() {
  const deviceInfo = getDeviceInfo()
  const width = deviceInfo.screenWidth
  
  if (width < 375) {
    return 'small-phone'
  } else if (width < 414) {
    return 'phone'
  } else if (width < 768) {
    return 'large-phone'
  } else if (width < 1024) {
    return 'tablet'
  } else {
    return 'desktop'
  }
}

/**
 * 判断是否为手机
 */
export function isPhone() {
  const deviceType = getDeviceType()
  return ['small-phone', 'phone', 'large-phone'].includes(deviceType)
}

/**
 * 判断是否为平板
 */
export function isTablet() {
  const deviceType = getDeviceType()
  return deviceType === 'tablet'
}

/**
 * 判断是否为桌面端
 */
export function isDesktop() {
  const deviceType = getDeviceType()
  return deviceType === 'desktop'
}

/**
 * 获取网格列数
 */
export function getGridColumns(defaultCols = 2) {
  const deviceType = getDeviceType()
  const colsMap = {
    'small-phone': 2,
    'phone': 2,
    'large-phone': 3,
    'tablet': 4,
    'desktop': 6
  }
  return colsMap[deviceType] || defaultCols
}

/**
 * 获取响应式字体大小
 */
export function getResponsiveFontSize(size = 'base') {
  const deviceType = getDeviceType()
  const fontSizes = {
    'small-phone': {
      'xs': '24rpx',
      'sm': '28rpx',
      'base': '30rpx',
      'lg': '32rpx',
      'xl': '36rpx'
    },
    'phone': {
      'xs': '26rpx',
      'sm': '30rpx',
      'base': '32rpx',
      'lg': '34rpx',
      'xl': '38rpx'
    },
    'large-phone': {
      'xs': '28rpx',
      'sm': '32rpx',
      'base': '34rpx',
      'lg': '36rpx',
      'xl': '40rpx'
    },
    'tablet': {
      'xs': '32rpx',
      'sm': '36rpx',
      'base': '40rpx',
      'lg': '44rpx',
      'xl': '48rpx'
    },
    'desktop': {
      'xs': '36rpx',
      'sm': '40rpx',
      'base': '44rpx',
      'lg': '48rpx',
      'xl': '52rpx'
    }
  }
  
  return fontSizes[deviceType]?.[size] || fontSizes['phone'][size] || '32rpx'
}

/**
 * 获取响应式间距
 */
export function getResponsiveSpacing(size = 'base') {
  const deviceType = getDeviceType()
  const spacings = {
    'small-phone': {
      'xs': '16rpx',
      'sm': '20rpx',
      'base': '24rpx',
      'lg': '32rpx',
      'xl': '40rpx'
    },
    'phone': {
      'xs': '20rpx',
      'sm': '24rpx',
      'base': '30rpx',
      'lg': '40rpx',
      'xl': '50rpx'
    },
    'large-phone': {
      'xs': '24rpx',
      'sm': '28rpx',
      'base': '36rpx',
      'lg': '48rpx',
      'xl': '60rpx'
    },
    'tablet': {
      'xs': '32rpx',
      'sm': '40rpx',
      'base': '48rpx',
      'lg': '64rpx',
      'xl': '80rpx'
    },
    'desktop': {
      'xs': '40rpx',
      'sm': '50rpx',
      'base': '60rpx',
      'lg': '80rpx',
      'xl': '100rpx'
    }
  }
  
  return spacings[deviceType]?.[size] || spacings['phone'][size] || '30rpx'
}

/**
 * 获取响应式图表高度
 */
export function getChartHeight() {
  const deviceType = getDeviceType()
  const heights = {
    'small-phone': '300px',
    'phone': '350px',
    'large-phone': '400px',
    'tablet': '450px',
    'desktop': '500px'
  }
  return heights[deviceType] || '350px'
}

/**
 * rpx转px
 */
export function rpxToPx(rpx) {
  const deviceInfo = getDeviceInfo()
  return (rpx / 750) * deviceInfo.screenWidth
}

/**
 * px转rpx
 */
export function pxToRpx(px) {
  const deviceInfo = getDeviceInfo()
  return (px / deviceInfo.screenWidth) * 750
}

/**
 * 获取安全区域高度
 */
export function getSafeAreaHeight() {
  const deviceInfo = getDeviceInfo()
  const safeAreaInsets = deviceInfo.safeAreaInsets
  return {
    top: safeAreaInsets.top || deviceInfo.statusBarHeight || 0,
    bottom: safeAreaInsets.bottom || 0
  }
}

/**
 * 获取导航栏高度
 */
export function getNavigationBarHeight() {
  const deviceInfo = getDeviceInfo()
  
  // #ifdef MP-WEIXIN
  try {
    const menuButton = uni.getMenuButtonBoundingClientRect()
    return menuButton.top + menuButton.height + (menuButton.top - deviceInfo.statusBarHeight)
  } catch (error) {
    return deviceInfo.statusBarHeight + 44
  }
  // #endif
  
  // #ifdef APP-PLUS
  return deviceInfo.statusBarHeight + 44
  // #endif
  
  // #ifdef H5
  return 44
  // #endif
  
  return 44
}

/**
 * 创建响应式样式对象
 */
export function createResponsiveStyle(config) {
  const deviceType = getDeviceType()
  
  if (typeof config === 'object' && config[deviceType]) {
    return config[deviceType]
  }
  
  if (typeof config === 'object' && config.default) {
    return config.default
  }
  
  return config
}

/**
 * 获取响应式类名
 */
export function getResponsiveClass(baseClass, config) {
  const deviceType = getDeviceType()
  const classes = [baseClass]
  
  if (config && config[deviceType]) {
    classes.push(config[deviceType])
  }
  
  return classes.join(' ')
}
