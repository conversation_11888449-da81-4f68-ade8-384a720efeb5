<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>响应式测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .device-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .test-grid {
            display: grid;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .test-item {
            background: #f0f0f0;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        
        /* 响应式网格 */
        @media (max-width: 374px) {
            .test-grid { grid-template-columns: 1fr; }
            .device-type::after { content: " (小屏手机)"; }
        }
        
        @media (min-width: 375px) and (max-width: 413px) {
            .test-grid { grid-template-columns: repeat(2, 1fr); }
            .device-type::after { content: " (标准手机)"; }
        }
        
        @media (min-width: 414px) and (max-width: 767px) {
            .test-grid { grid-template-columns: repeat(3, 1fr); }
            .device-type::after { content: " (大屏手机)"; }
        }
        
        @media (min-width: 768px) and (max-width: 1023px) {
            .test-grid { grid-template-columns: repeat(4, 1fr); }
            .device-type::after { content: " (平板)"; }
        }
        
        @media (min-width: 1024px) {
            .test-grid { grid-template-columns: repeat(6, 1fr); }
            .device-type::after { content: " (桌面)"; }
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>移动端响应式测试</h1>
        
        <div class="device-info">
            <h3>设备信息</h3>
            <p>屏幕宽度: <span id="screenWidth"></span>px</p>
            <p>屏幕高度: <span id="screenHeight"></span>px</p>
            <p>设备类型: <span class="device-type" id="deviceType"></span></p>
            <p>用户代理: <span id="userAgent"></span></p>
        </div>
        
        <div class="status success">
            <strong>✓ 响应式CSS样式已加载</strong>
            <p>如果您能看到这个页面，说明基础的响应式系统正在工作。</p>
        </div>
        
        <h3>响应式网格测试</h3>
        <p>以下网格会根据屏幕尺寸自动调整列数：</p>
        
        <div class="test-grid">
            <div class="test-item">项目 1</div>
            <div class="test-item">项目 2</div>
            <div class="test-item">项目 3</div>
            <div class="test-item">项目 4</div>
            <div class="test-item">项目 5</div>
            <div class="test-item">项目 6</div>
        </div>
        
        <h3>测试说明</h3>
        <ul>
            <li><strong>小屏手机 (&lt;375px)</strong>: 1列网格</li>
            <li><strong>标准手机 (375-413px)</strong>: 2列网格</li>
            <li><strong>大屏手机 (414-767px)</strong>: 3列网格</li>
            <li><strong>平板 (768-1023px)</strong>: 4列网格</li>
            <li><strong>桌面 (&gt;1024px)</strong>: 6列网格</li>
        </ul>
        
        <h3>下一步</h3>
        <p>如果这个测试页面正常显示，说明响应式系统基础功能正常。您可以：</p>
        <ol>
            <li>在不同设备上测试uni-app项目</li>
            <li>检查控制台是否有JavaScript错误</li>
            <li>验证响应式组件是否正确加载</li>
        </ol>
        
        <div id="errorInfo" class="error" style="display: none;">
            <strong>⚠ 检测到问题</strong>
            <p id="errorMessage"></p>
        </div>
    </div>
    
    <script>
        // 获取设备信息
        function getDeviceInfo() {
            const width = window.innerWidth || document.documentElement.clientWidth;
            const height = window.innerHeight || document.documentElement.clientHeight;
            
            let deviceType = '';
            if (width < 375) {
                deviceType = 'small-phone';
            } else if (width < 414) {
                deviceType = 'phone';
            } else if (width < 768) {
                deviceType = 'large-phone';
            } else if (width < 1024) {
                deviceType = 'tablet';
            } else {
                deviceType = 'desktop';
            }
            
            return {
                width,
                height,
                deviceType,
                userAgent: navigator.userAgent
            };
        }
        
        // 更新页面信息
        function updateDeviceInfo() {
            const info = getDeviceInfo();
            
            document.getElementById('screenWidth').textContent = info.width;
            document.getElementById('screenHeight').textContent = info.height;
            document.getElementById('deviceType').textContent = info.deviceType;
            document.getElementById('userAgent').textContent = info.userAgent.substring(0, 100) + '...';
        }
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            updateDeviceInfo();
            
            // 监听窗口大小变化
            window.addEventListener('resize', updateDeviceInfo);
            
            console.log('响应式测试页面加载完成');
        });
        
        // 错误处理
        window.addEventListener('error', function(e) {
            const errorDiv = document.getElementById('errorInfo');
            const errorMessage = document.getElementById('errorMessage');
            
            errorMessage.textContent = '发生错误: ' + e.message;
            errorDiv.style.display = 'block';
            
            console.error('页面错误:', e);
        });
    </script>
</body>
</html>
