/**
 * 移动端响应式设计样式
 * 安全的、渐进式的响应式解决方案
 * 注意：所有样式都使用较低的优先级，避免覆盖现有样式
 */

/* ==================
   基础重置和兼容性
==================== */

// 确保基础元素正常显示
.responsive-container,
.responsive-grid,
.responsive-card {
  box-sizing: border-box;
}

// 确保文本正常显示
.responsive-text-xs,
.responsive-text-sm,
.responsive-text-base,
.responsive-text-lg,
.responsive-text-xl,
.responsive-text-2xl {
  line-height: 1.4;
  color: inherit;
}

/* ==================
   设备断点定义
==================== */

// 基于常见设备尺寸的断点
$mobile-small: 320px;   // 小屏手机
$mobile-medium: 375px;  // 标准手机
$mobile-large: 414px;   // 大屏手机
$tablet-small: 768px;   // 小平板
$tablet-large: 1024px;  // 大平板
$desktop: 1200px;       // 桌面

/* ==================
   响应式容器
==================== */

.responsive-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24rpx;
  
  @media (min-width: $mobile-medium) {
    padding: 0 30rpx;
  }
  
  @media (min-width: $tablet-small) {
    padding: 0 40rpx;
  }
  
  @media (min-width: $tablet-large) {
    padding: 0 60rpx;
  }
}

/* ==================
   响应式网格系统
==================== */

.responsive-grid {
  display: flex;
  flex-wrap: wrap;
  margin: -12rpx;
  
  @media (min-width: $mobile-medium) {
    margin: -16rpx;
  }
  
  @media (min-width: $tablet-small) {
    margin: -20rpx;
  }
}

.responsive-grid-item {
  padding: 12rpx;
  
  @media (min-width: $mobile-medium) {
    padding: 16rpx;
  }
  
  @media (min-width: $tablet-small) {
    padding: 20rpx;
  }
}

// 网格列数类
.grid-col-1 { flex: 0 0 100%; max-width: 100%; }
.grid-col-2 { flex: 0 0 50%; max-width: 50%; }
.grid-col-3 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.grid-col-4 { flex: 0 0 25%; max-width: 25%; }
.grid-col-6 { flex: 0 0 16.666667%; max-width: 16.666667%; }

// 响应式网格列数
@media (min-width: $mobile-medium) {
  .grid-sm-1 { flex: 0 0 100%; max-width: 100%; }
  .grid-sm-2 { flex: 0 0 50%; max-width: 50%; }
  .grid-sm-3 { flex: 0 0 33.333333%; max-width: 33.333333%; }
  .grid-sm-4 { flex: 0 0 25%; max-width: 25%; }
  .grid-sm-6 { flex: 0 0 16.666667%; max-width: 16.666667%; }
}

@media (min-width: $tablet-small) {
  .grid-md-1 { flex: 0 0 100%; max-width: 100%; }
  .grid-md-2 { flex: 0 0 50%; max-width: 50%; }
  .grid-md-3 { flex: 0 0 33.333333%; max-width: 33.333333%; }
  .grid-md-4 { flex: 0 0 25%; max-width: 25%; }
  .grid-md-6 { flex: 0 0 16.666667%; max-width: 16.666667%; }
}

@media (min-width: $tablet-large) {
  .grid-lg-1 { flex: 0 0 100%; max-width: 100%; }
  .grid-lg-2 { flex: 0 0 50%; max-width: 50%; }
  .grid-lg-3 { flex: 0 0 33.333333%; max-width: 33.333333%; }
  .grid-lg-4 { flex: 0 0 25%; max-width: 25%; }
  .grid-lg-6 { flex: 0 0 16.666667%; max-width: 16.666667%; }
}

/* ==================
   响应式字体大小
==================== */

.responsive-text-xs { font-size: 24rpx; }
.responsive-text-sm { font-size: 28rpx; }
.responsive-text-base { font-size: 30rpx; }
.responsive-text-lg { font-size: 32rpx; }
.responsive-text-xl { font-size: 36rpx; }
.responsive-text-2xl { font-size: 40rpx; }

@media (min-width: $mobile-medium) {
  .responsive-text-xs { font-size: 26rpx; }
  .responsive-text-sm { font-size: 30rpx; }
  .responsive-text-base { font-size: 32rpx; }
  .responsive-text-lg { font-size: 34rpx; }
  .responsive-text-xl { font-size: 38rpx; }
  .responsive-text-2xl { font-size: 42rpx; }
}

@media (min-width: $tablet-small) {
  .responsive-text-xs { font-size: 28rpx; }
  .responsive-text-sm { font-size: 32rpx; }
  .responsive-text-base { font-size: 36rpx; }
  .responsive-text-lg { font-size: 40rpx; }
  .responsive-text-xl { font-size: 44rpx; }
  .responsive-text-2xl { font-size: 48rpx; }
}

@media (min-width: $tablet-large) {
  .responsive-text-xs { font-size: 30rpx; }
  .responsive-text-sm { font-size: 36rpx; }
  .responsive-text-base { font-size: 40rpx; }
  .responsive-text-lg { font-size: 44rpx; }
  .responsive-text-xl { font-size: 48rpx; }
  .responsive-text-2xl { font-size: 52rpx; }
}

/* ==================
   响应式间距
==================== */

.responsive-p-xs { padding: 16rpx; }
.responsive-p-sm { padding: 20rpx; }
.responsive-p-base { padding: 24rpx; }
.responsive-p-lg { padding: 32rpx; }
.responsive-p-xl { padding: 40rpx; }

@media (min-width: $mobile-medium) {
  .responsive-p-xs { padding: 20rpx; }
  .responsive-p-sm { padding: 24rpx; }
  .responsive-p-base { padding: 30rpx; }
  .responsive-p-lg { padding: 40rpx; }
  .responsive-p-xl { padding: 50rpx; }
}

@media (min-width: $tablet-small) {
  .responsive-p-xs { padding: 24rpx; }
  .responsive-p-sm { padding: 32rpx; }
  .responsive-p-base { padding: 40rpx; }
  .responsive-p-lg { padding: 56rpx; }
  .responsive-p-xl { padding: 72rpx; }
}

.responsive-m-xs { margin: 16rpx; }
.responsive-m-sm { margin: 20rpx; }
.responsive-m-base { margin: 24rpx; }
.responsive-m-lg { margin: 32rpx; }
.responsive-m-xl { margin: 40rpx; }

@media (min-width: $mobile-medium) {
  .responsive-m-xs { margin: 20rpx; }
  .responsive-m-sm { margin: 24rpx; }
  .responsive-m-base { margin: 30rpx; }
  .responsive-m-lg { margin: 40rpx; }
  .responsive-m-xl { margin: 50rpx; }
}

@media (min-width: $tablet-small) {
  .responsive-m-xs { margin: 24rpx; }
  .responsive-m-sm { margin: 32rpx; }
  .responsive-m-base { margin: 40rpx; }
  .responsive-m-lg { margin: 56rpx; }
  .responsive-m-xl { margin: 72rpx; }
}

/* ==================
   响应式显示控制
==================== */

// 隐藏类
.hide-mobile { display: none; }
.hide-tablet { display: block; }
.hide-desktop { display: block; }

@media (min-width: $tablet-small) {
  .hide-mobile { display: block; }
  .hide-tablet { display: none; }
  .hide-desktop { display: block; }
}

@media (min-width: $desktop) {
  .hide-mobile { display: block; }
  .hide-tablet { display: block; }
  .hide-desktop { display: none; }
}

// 显示类
.show-mobile { display: block; }
.show-tablet { display: none; }
.show-desktop { display: none; }

@media (min-width: $tablet-small) {
  .show-mobile { display: none; }
  .show-tablet { display: block; }
  .show-desktop { display: none; }
}

@media (min-width: $desktop) {
  .show-mobile { display: none; }
  .show-tablet { display: none; }
  .show-desktop { display: block; }
}

/* ==================
   响应式卡片
==================== */

.responsive-card {
  background: #fff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 24rpx;
  
  @media (min-width: $mobile-medium) {
    border-radius: 12rpx;
    margin-bottom: 30rpx;
  }
  
  @media (min-width: $tablet-small) {
    border-radius: 16rpx;
    margin-bottom: 40rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  }
}

.responsive-card-header {
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  
  @media (min-width: $mobile-medium) {
    padding: 30rpx 36rpx;
  }
  
  @media (min-width: $tablet-small) {
    padding: 40rpx 48rpx;
  }
}

.responsive-card-body {
  padding: 24rpx 30rpx;
  
  @media (min-width: $mobile-medium) {
    padding: 30rpx 36rpx;
  }
  
  @media (min-width: $tablet-small) {
    padding: 40rpx 48rpx;
  }
}

.responsive-card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  
  @media (min-width: $mobile-medium) {
    font-size: 36rpx;
  }
  
  @media (min-width: $tablet-small) {
    font-size: 40rpx;
    margin-bottom: 12rpx;
  }
}

.responsive-card-subtitle {
  font-size: 26rpx;
  color: #999;
  
  @media (min-width: $mobile-medium) {
    font-size: 28rpx;
  }
  
  @media (min-width: $tablet-small) {
    font-size: 32rpx;
  }
}

/* ==================
   响应式按钮
==================== */

.responsive-button {
  padding: 24rpx 32rpx;
  font-size: 28rpx;
  border-radius: 8rpx;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  
  @media (min-width: $mobile-medium) {
    padding: 28rpx 40rpx;
    font-size: 30rpx;
    border-radius: 10rpx;
  }
  
  @media (min-width: $tablet-small) {
    padding: 32rpx 48rpx;
    font-size: 32rpx;
    border-radius: 12rpx;
  }
}

/* ==================
   响应式图表容器
==================== */

.responsive-chart {
  width: 100%;
  height: 400rpx;
  
  @media (min-width: $mobile-medium) {
    height: 460rpx;
  }
  
  @media (min-width: $tablet-small) {
    height: 520rpx;
  }
  
  @media (min-width: $tablet-large) {
    height: 600rpx;
  }
}
