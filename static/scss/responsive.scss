/**
 * 移动端响应式设计系统
 * 支持手机、平板等不同设备的自适应布局
 */

/* ==================
   设备断点定义
==================== */

// 设备尺寸断点 (基于实际设备宽度)
$breakpoints: (
  'xs': 320px,    // 小屏手机 (iPhone SE)
  'sm': 375px,    // 标准手机 (iPhone 6/7/8)
  'md': 414px,    // 大屏手机 (iPhone 6/7/8 Plus)
  'lg': 768px,    // 小平板 (iPad mini)
  'xl': 1024px,   // 大平板 (iPad)
  'xxl': 1366px   // 大屏设备
);

// 容器最大宽度
$container-max-widths: (
  'sm': 540px,
  'md': 720px,
  'lg': 960px,
  'xl': 1140px,
  'xxl': 1320px
);

/* ==================
   媒体查询混合器
==================== */

// 最小宽度媒体查询
@mixin media-up($name) {
  @if map-has-key($breakpoints, $name) {
    @media (min-width: #{map-get($breakpoints, $name)}) {
      @content;
    }
  } @else {
    @warn "Breakpoint #{$name} not found in $breakpoints.";
  }
}

// 最大宽度媒体查询
@mixin media-down($name) {
  @if map-has-key($breakpoints, $name) {
    @media (max-width: #{map-get($breakpoints, $name) - 1px}) {
      @content;
    }
  } @else {
    @warn "Breakpoint #{$name} not found in $breakpoints.";
  }
}

// 区间媒体查询
@mixin media-between($lower, $upper) {
  @if map-has-key($breakpoints, $lower) and map-has-key($breakpoints, $upper) {
    @media (min-width: #{map-get($breakpoints, $lower)}) and (max-width: #{map-get($breakpoints, $upper) - 1px}) {
      @content;
    }
  } @else {
    @warn "Breakpoint #{$lower} or #{$upper} not found in $breakpoints.";
  }
}

// 仅指定断点
@mixin media-only($name) {
  @if $name == 'xs' {
    @include media-down('sm') {
      @content;
    }
  } @else if $name == 'xxl' {
    @include media-up('xxl') {
      @content;
    }
  } @else {
    $breakpoint-names: map-keys($breakpoints);
    $index: index($breakpoint-names, $name);
    @if $index {
      $next-breakpoint: nth($breakpoint-names, $index + 1);
      @include media-between($name, $next-breakpoint) {
        @content;
      }
    }
  }
}

/* ==================
   响应式容器
==================== */

.container-responsive {
  width: 100%;
  padding-left: 15px;
  padding-right: 15px;
  margin-left: auto;
  margin-right: auto;

  @each $breakpoint, $max-width in $container-max-widths {
    @include media-up($breakpoint) {
      max-width: $max-width;
    }
  }
}

/* ==================
   响应式网格系统
==================== */

.row-responsive {
  display: flex;
  flex-wrap: wrap;
  margin-left: -15px;
  margin-right: -15px;
}

.col-responsive {
  position: relative;
  width: 100%;
  padding-left: 15px;
  padding-right: 15px;
}

// 生成响应式列类
@each $breakpoint in map-keys($breakpoints) {
  @include media-up($breakpoint) {
    @for $i from 1 through 12 {
      .col-#{$breakpoint}-#{$i} {
        flex: 0 0 percentage($i / 12);
        max-width: percentage($i / 12);
      }
    }
  }
}

/* ==================
   响应式字体大小
==================== */

// 基础字体大小映射
$font-sizes: (
  'xs': (
    'small': 24rpx,
    'base': 28rpx,
    'large': 32rpx,
    'xl': 36rpx,
    'xxl': 40rpx
  ),
  'sm': (
    'small': 26rpx,
    'base': 30rpx,
    'large': 34rpx,
    'xl': 38rpx,
    'xxl': 42rpx
  ),
  'md': (
    'small': 28rpx,
    'base': 32rpx,
    'large': 36rpx,
    'xl': 40rpx,
    'xxl': 44rpx
  ),
  'lg': (
    'small': 32rpx,
    'base': 36rpx,
    'large': 40rpx,
    'xl': 44rpx,
    'xxl': 48rpx
  ),
  'xl': (
    'small': 36rpx,
    'base': 40rpx,
    'large': 44rpx,
    'xl': 48rpx,
    'xxl': 52rpx
  )
);

// 生成响应式字体类
@each $breakpoint, $sizes in $font-sizes {
  @include media-up($breakpoint) {
    @each $size-name, $size-value in $sizes {
      .text-#{$breakpoint}-#{$size-name} {
        font-size: $size-value !important;
      }
    }
  }
}

/* ==================
   响应式间距
==================== */

// 间距值映射
$spacings: (
  'xs': (
    'small': 16rpx,
    'base': 24rpx,
    'large': 32rpx,
    'xl': 40rpx
  ),
  'sm': (
    'small': 20rpx,
    'base': 30rpx,
    'large': 40rpx,
    'xl': 50rpx
  ),
  'md': (
    'small': 24rpx,
    'base': 36rpx,
    'large': 48rpx,
    'xl': 60rpx
  ),
  'lg': (
    'small': 32rpx,
    'base': 48rpx,
    'large': 64rpx,
    'xl': 80rpx
  ),
  'xl': (
    'small': 40rpx,
    'base': 60rpx,
    'large': 80rpx,
    'xl': 100rpx
  )
);

// 生成响应式间距类
@each $breakpoint, $sizes in $spacings {
  @include media-up($breakpoint) {
    @each $size-name, $size-value in $sizes {
      .p-#{$breakpoint}-#{$size-name} { padding: $size-value !important; }
      .pt-#{$breakpoint}-#{$size-name} { padding-top: $size-value !important; }
      .pb-#{$breakpoint}-#{$size-name} { padding-bottom: $size-value !important; }
      .pl-#{$breakpoint}-#{$size-name} { padding-left: $size-value !important; }
      .pr-#{$breakpoint}-#{$size-name} { padding-right: $size-value !important; }
      .px-#{$breakpoint}-#{$size-name} { 
        padding-left: $size-value !important; 
        padding-right: $size-value !important; 
      }
      .py-#{$breakpoint}-#{$size-name} { 
        padding-top: $size-value !important; 
        padding-bottom: $size-value !important; 
      }
      
      .m-#{$breakpoint}-#{$size-name} { margin: $size-value !important; }
      .mt-#{$breakpoint}-#{$size-name} { margin-top: $size-value !important; }
      .mb-#{$breakpoint}-#{$size-name} { margin-bottom: $size-value !important; }
      .ml-#{$breakpoint}-#{$size-name} { margin-left: $size-value !important; }
      .mr-#{$breakpoint}-#{$size-name} { margin-right: $size-value !important; }
      .mx-#{$breakpoint}-#{$size-name} { 
        margin-left: $size-value !important; 
        margin-right: $size-value !important; 
      }
      .my-#{$breakpoint}-#{$size-name} { 
        margin-top: $size-value !important; 
        margin-bottom: $size-value !important; 
      }
    }
  }
}

/* ==================
   响应式显示/隐藏
==================== */

@each $breakpoint in map-keys($breakpoints) {
  @include media-up($breakpoint) {
    .d-#{$breakpoint}-none { display: none !important; }
    .d-#{$breakpoint}-block { display: block !important; }
    .d-#{$breakpoint}-flex { display: flex !important; }
    .d-#{$breakpoint}-inline { display: inline !important; }
    .d-#{$breakpoint}-inline-block { display: inline-block !important; }
  }
  
  @include media-down($breakpoint) {
    .d-#{$breakpoint}-down-none { display: none !important; }
    .d-#{$breakpoint}-down-block { display: block !important; }
    .d-#{$breakpoint}-down-flex { display: flex !important; }
  }
}

/* ==================
   响应式工具类
==================== */

// 文本对齐
@each $breakpoint in map-keys($breakpoints) {
  @include media-up($breakpoint) {
    .text-#{$breakpoint}-left { text-align: left !important; }
    .text-#{$breakpoint}-center { text-align: center !important; }
    .text-#{$breakpoint}-right { text-align: right !important; }
  }
}

// Flex布局
@each $breakpoint in map-keys($breakpoints) {
  @include media-up($breakpoint) {
    .flex-#{$breakpoint}-row { flex-direction: row !important; }
    .flex-#{$breakpoint}-column { flex-direction: column !important; }
    .justify-#{$breakpoint}-start { justify-content: flex-start !important; }
    .justify-#{$breakpoint}-center { justify-content: center !important; }
    .justify-#{$breakpoint}-end { justify-content: flex-end !important; }
    .justify-#{$breakpoint}-between { justify-content: space-between !important; }
    .justify-#{$breakpoint}-around { justify-content: space-around !important; }
    .align-#{$breakpoint}-start { align-items: flex-start !important; }
    .align-#{$breakpoint}-center { align-items: center !important; }
    .align-#{$breakpoint}-end { align-items: flex-end !important; }
  }
}

/* ==================
   响应式增强样式
==================== */

// 响应式图片
.responsive-image {
  width: 100%;
  height: auto;
  display: block;

  @include media-up('lg') {
    max-width: 100%;
  }
}

// 响应式视频
.responsive-video {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; // 16:9 aspect ratio

  video, iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}

// 响应式表格
.responsive-table {
  width: 100%;
  overflow-x: auto;

  table {
    width: 100%;
    min-width: 600px;

    @include media-up('lg') {
      min-width: auto;
    }
  }
}

// 响应式卡片布局
.responsive-cards {
  display: grid;
  gap: 20rpx;
  grid-template-columns: 1fr;

  @include media-up('sm') {
    grid-template-columns: repeat(2, 1fr);
  }

  @include media-up('md') {
    grid-template-columns: repeat(3, 1fr);
  }

  @include media-up('lg') {
    grid-template-columns: repeat(4, 1fr);
  }

  @include media-up('xl') {
    grid-template-columns: repeat(6, 1fr);
  }
}

// 响应式导航
.responsive-nav {
  display: flex;
  flex-direction: column;

  @include media-up('md') {
    flex-direction: row;
  }

  .nav-item {
    padding: 16rpx;
    text-align: center;

    @include media-up('md') {
      padding: 24rpx 32rpx;
    }
  }
}

// 响应式按钮
.responsive-button {
  width: 100%;
  padding: 24rpx;
  font-size: 28rpx;

  @include media-up('sm') {
    width: auto;
    min-width: 200rpx;
    padding: 28rpx 40rpx;
    font-size: 30rpx;
  }

  @include media-up('lg') {
    padding: 32rpx 48rpx;
    font-size: 32rpx;
  }
}

// 响应式输入框
.responsive-input {
  width: 100%;
  padding: 24rpx;
  font-size: 28rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;

  @include media-up('sm') {
    padding: 28rpx;
    font-size: 30rpx;
  }

  @include media-up('lg') {
    padding: 32rpx;
    font-size: 32rpx;
    border-radius: 12rpx;
  }
}

// 响应式模态框
.responsive-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.5);

  .modal-content {
    width: 90%;
    max-width: 500px;
    max-height: 80%;
    background: white;
    border-radius: 12rpx;
    overflow: hidden;

    @include media-up('sm') {
      width: 80%;
      border-radius: 16rpx;
    }

    @include media-up('md') {
      width: 60%;
      max-width: 600px;
    }

    @include media-up('lg') {
      width: 50%;
      max-width: 800px;
      border-radius: 20rpx;
    }
  }
}
