<template>
  <view class="responsive-grid" :style="gridStyle">
    <slot></slot>
  </view>
</template>

<script>
import deviceDetector from '@/utils/device'

export default {
  name: 'ResponsiveGrid',
  props: {
    // 列数配置 - 可以是数字或对象
    cols: {
      type: [Number, Object],
      default: () => ({
        xs: 1,
        sm: 2,
        md: 3,
        lg: 4,
        xl: 6
      })
    },
    // 间距
    gap: {
      type: [String, Number],
      default: '24rpx'
    },
    // 是否自动换行
    wrap: {
      type: Boolean,
      default: true
    },
    // 对齐方式
    align: {
      type: String,
      default: 'stretch',
      validator: value => ['stretch', 'start', 'center', 'end'].includes(value)
    },
    // 水平对齐
    justify: {
      type: String,
      default: 'start',
      validator: value => ['start', 'center', 'end', 'space-between', 'space-around', 'space-evenly'].includes(value)
    }
  },
  data() {
    return {
      screenSize: '',
      currentCols: 2
    }
  },
  computed: {
    gridStyle() {
      const styles = {
        display: 'grid',
        gap: this.gap,
        gridTemplateColumns: `repeat(${this.currentCols}, 1fr)`,
        alignItems: this.align,
        justifyContent: this.justify
      }
      
      return styles
    }
  },
  created() {
    this.initGrid()
  },
  methods: {
    initGrid() {
      this.screenSize = deviceDetector.getScreenSize()
      this.updateCols()
      
      // 监听屏幕变化
      deviceDetector.onScreenChange(() => {
        this.screenSize = deviceDetector.getScreenSize()
        this.updateCols()
      })
    },
    
    updateCols() {
      if (typeof this.cols === 'number') {
        this.currentCols = this.cols
      } else if (typeof this.cols === 'object') {
        // 根据屏幕尺寸获取对应的列数
        const breakpoints = ['xs', 'sm', 'md', 'lg', 'xl']
        const currentIndex = breakpoints.indexOf(this.screenSize)
        
        // 从当前断点向下查找可用的列数配置
        for (let i = currentIndex; i >= 0; i--) {
          const breakpoint = breakpoints[i]
          if (this.cols[breakpoint] !== undefined) {
            this.currentCols = this.cols[breakpoint]
            break
          }
        }
        
        // 如果没有找到，使用默认值
        if (this.currentCols === undefined) {
          this.currentCols = 2
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.responsive-grid {
  width: 100%;
  box-sizing: border-box;
}
</style>
