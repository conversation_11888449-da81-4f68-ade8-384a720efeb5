<template>
  <view 
    class="responsive-layout" 
    :class="[
      `device-${deviceType}`,
      `screen-${screenSize}`,
      customClass
    ]"
    :style="layoutStyle"
  >
    <slot></slot>
  </view>
</template>

<script>
import deviceDetector from '@/utils/device'

export default {
  name: 'ResponsiveLayout',
  props: {
    // 自定义类名
    customClass: {
      type: String,
      default: ''
    },
    // 是否启用容器最大宽度限制
    container: {
      type: Boolean,
      default: false
    },
    // 内边距配置
    padding: {
      type: [String, Object],
      default: 'base'
    },
    // 外边距配置
    margin: {
      type: [String, Object],
      default: ''
    },
    // 背景色
    backgroundColor: {
      type: String,
      default: ''
    },
    // 最小高度
    minHeight: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      deviceType: '',
      screenSize: '',
      systemInfo: null
    }
  },
  computed: {
    layoutStyle() {
      const styles = {}
      
      // 背景色
      if (this.backgroundColor) {
        styles.backgroundColor = this.backgroundColor
      }
      
      // 最小高度
      if (this.minHeight) {
        styles.minHeight = this.minHeight
      }
      
      // 容器最大宽度
      if (this.container) {
        const maxWidths = {
          'xs': '100%',
          'sm': '540px',
          'md': '720px',
          'lg': '960px',
          'xl': '1140px'
        }
        styles.maxWidth = maxWidths[this.screenSize] || '100%'
        styles.marginLeft = 'auto'
        styles.marginRight = 'auto'
      }
      
      // 内边距
      if (this.padding) {
        const paddingValue = this.getPaddingValue()
        if (typeof paddingValue === 'string') {
          styles.padding = paddingValue
        } else {
          Object.assign(styles, paddingValue)
        }
      }
      
      // 外边距
      if (this.margin) {
        const marginValue = this.getMarginValue()
        if (typeof marginValue === 'string') {
          styles.margin = marginValue
        } else {
          Object.assign(styles, marginValue)
        }
      }
      
      return styles
    }
  },
  created() {
    this.initDevice()
  },
  methods: {
    initDevice() {
      this.deviceType = deviceDetector.getDeviceType()
      this.screenSize = deviceDetector.getScreenSize()
      this.systemInfo = deviceDetector.getSystemInfo()
      
      // 监听屏幕变化
      deviceDetector.onScreenChange((deviceType, screenSize) => {
        this.deviceType = deviceType
        this.screenSize = screenSize
      })
    },
    
    getPaddingValue() {
      if (typeof this.padding === 'string') {
        return deviceDetector.getPadding(this.padding)
      } else if (typeof this.padding === 'object') {
        const result = {}
        Object.keys(this.padding).forEach(key => {
          const value = this.padding[key]
          if (typeof value === 'string' && ['small', 'base', 'large'].includes(value)) {
            result[`padding${key.charAt(0).toUpperCase() + key.slice(1)}`] = deviceDetector.getPadding(value)
          } else {
            result[`padding${key.charAt(0).toUpperCase() + key.slice(1)}`] = value
          }
        })
        return result
      }
      return ''
    },
    
    getMarginValue() {
      if (typeof this.margin === 'string') {
        return this.margin
      } else if (typeof this.margin === 'object') {
        const result = {}
        Object.keys(this.margin).forEach(key => {
          result[`margin${key.charAt(0).toUpperCase() + key.slice(1)}`] = this.margin[key]
        })
        return result
      }
      return ''
    }
  }
}
</script>

<style lang="scss" scoped>
.responsive-layout {
  width: 100%;
  box-sizing: border-box;
  
  // 设备特定样式
  &.device-small-phone {
    .responsive-text {
      font-size: 28rpx;
    }
  }
  
  &.device-phone {
    .responsive-text {
      font-size: 30rpx;
    }
  }
  
  &.device-large-phone {
    .responsive-text {
      font-size: 32rpx;
    }
  }
  
  &.device-tablet {
    .responsive-text {
      font-size: 36rpx;
    }
  }
  
  &.device-desktop {
    .responsive-text {
      font-size: 40rpx;
    }
  }
  
  // 屏幕尺寸特定样式
  &.screen-xs {
    --grid-cols: 2;
    --card-spacing: 16rpx;
  }
  
  &.screen-sm {
    --grid-cols: 2;
    --card-spacing: 20rpx;
  }
  
  &.screen-md {
    --grid-cols: 3;
    --card-spacing: 24rpx;
  }
  
  &.screen-lg {
    --grid-cols: 4;
    --card-spacing: 32rpx;
  }
  
  &.screen-xl {
    --grid-cols: 6;
    --card-spacing: 40rpx;
  }
}
</style>
