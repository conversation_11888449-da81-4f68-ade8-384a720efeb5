<template>
  <view 
    class="responsive-card" 
    :class="[
      `card-${size}`,
      `device-${deviceType}`,
      shadow ? 'card-shadow' : '',
      customClass
    ]"
    :style="cardStyle"
    @click="handleClick"
  >
    <!-- 卡片头部 -->
    <view v-if="$slots.header || title" class="card-header" :style="headerStyle">
      <slot name="header">
        <view class="card-title" :style="titleStyle">{{ title }}</view>
        <view v-if="subtitle" class="card-subtitle" :style="subtitleStyle">{{ subtitle }}</view>
      </slot>
    </view>
    
    <!-- 卡片内容 -->
    <view class="card-body" :style="bodyStyle">
      <slot></slot>
    </view>
    
    <!-- 卡片底部 -->
    <view v-if="$slots.footer" class="card-footer" :style="footerStyle">
      <slot name="footer"></slot>
    </view>
  </view>
</template>

<script>
import deviceDetector from '@/utils/device'

export default {
  name: 'ResponsiveCard',
  props: {
    // 标题
    title: {
      type: String,
      default: ''
    },
    // 副标题
    subtitle: {
      type: String,
      default: ''
    },
    // 卡片尺寸
    size: {
      type: String,
      default: 'medium',
      validator: value => ['small', 'medium', 'large'].includes(value)
    },
    // 是否显示阴影
    shadow: {
      type: Boolean,
      default: true
    },
    // 背景色
    backgroundColor: {
      type: String,
      default: '#ffffff'
    },
    // 边框圆角
    borderRadius: {
      type: String,
      default: ''
    },
    // 自定义类名
    customClass: {
      type: String,
      default: ''
    },
    // 是否可点击
    clickable: {
      type: Boolean,
      default: false
    },
    // 内边距配置
    padding: {
      type: [String, Object],
      default: 'base'
    },
    // 外边距配置
    margin: {
      type: [String, Object],
      default: ''
    }
  },
  data() {
    return {
      deviceType: '',
      screenSize: ''
    }
  },
  computed: {
    cardStyle() {
      const styles = {
        backgroundColor: this.backgroundColor
      }
      
      // 边框圆角
      if (this.borderRadius) {
        styles.borderRadius = this.borderRadius
      } else {
        // 根据设备类型设置默认圆角
        const radiusMap = {
          'small-phone': '8rpx',
          'phone': '10rpx',
          'large-phone': '12rpx',
          'tablet': '16rpx',
          'desktop': '20rpx'
        }
        styles.borderRadius = radiusMap[this.deviceType] || '10rpx'
      }
      
      // 外边距
      if (this.margin) {
        const marginValue = this.getMarginValue()
        if (typeof marginValue === 'string') {
          styles.margin = marginValue
        } else {
          Object.assign(styles, marginValue)
        }
      }
      
      // 可点击样式
      if (this.clickable) {
        styles.cursor = 'pointer'
      }
      
      return styles
    },
    
    headerStyle() {
      return {
        padding: this.getPaddingValue('header')
      }
    },
    
    bodyStyle() {
      return {
        padding: this.getPaddingValue('body')
      }
    },
    
    footerStyle() {
      return {
        padding: this.getPaddingValue('footer')
      }
    },
    
    titleStyle() {
      return {
        fontSize: deviceDetector.getFontSize('large'),
        fontWeight: '600',
        color: '#333333'
      }
    },
    
    subtitleStyle() {
      return {
        fontSize: deviceDetector.getFontSize('small'),
        color: '#999999',
        marginTop: '8rpx'
      }
    }
  },
  created() {
    this.initDevice()
  },
  methods: {
    initDevice() {
      this.deviceType = deviceDetector.getDeviceType()
      this.screenSize = deviceDetector.getScreenSize()
      
      // 监听屏幕变化
      deviceDetector.onScreenChange((deviceType, screenSize) => {
        this.deviceType = deviceType
        this.screenSize = screenSize
      })
    },
    
    getPaddingValue(section = 'body') {
      let paddingSize = this.padding
      
      if (typeof paddingSize === 'object' && paddingSize[section]) {
        paddingSize = paddingSize[section]
      }
      
      if (typeof paddingSize === 'string' && ['small', 'base', 'large'].includes(paddingSize)) {
        return deviceDetector.getPadding(paddingSize)
      }
      
      return paddingSize || deviceDetector.getPadding('base')
    },
    
    getMarginValue() {
      if (typeof this.margin === 'string') {
        return this.margin
      } else if (typeof this.margin === 'object') {
        const result = {}
        Object.keys(this.margin).forEach(key => {
          result[`margin${key.charAt(0).toUpperCase() + key.slice(1)}`] = this.margin[key]
        })
        return result
      }
      return ''
    },
    
    handleClick(event) {
      if (this.clickable) {
        this.$emit('click', event)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.responsive-card {
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  transition: all 0.3s ease;
  
  &.card-shadow {
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  }
  
  &.clickable:active {
    transform: scale(0.98);
    opacity: 0.9;
  }
  
  .card-header {
    border-bottom: 1rpx solid #f0f0f0;
    
    .card-title {
      line-height: 1.4;
    }
    
    .card-subtitle {
      line-height: 1.3;
    }
  }
  
  .card-body {
    flex: 1;
  }
  
  .card-footer {
    border-top: 1rpx solid #f0f0f0;
  }
  
  // 尺寸变体
  &.card-small {
    .card-header,
    .card-body,
    .card-footer {
      padding: 16rpx 20rpx;
    }
  }
  
  &.card-medium {
    .card-header,
    .card-body,
    .card-footer {
      padding: 24rpx 30rpx;
    }
  }
  
  &.card-large {
    .card-header,
    .card-body,
    .card-footer {
      padding: 32rpx 40rpx;
    }
  }
  
  // 设备特定样式
  &.device-small-phone {
    .card-title {
      font-size: 28rpx;
    }
    .card-subtitle {
      font-size: 24rpx;
    }
  }
  
  &.device-phone {
    .card-title {
      font-size: 30rpx;
    }
    .card-subtitle {
      font-size: 26rpx;
    }
  }
  
  &.device-large-phone {
    .card-title {
      font-size: 32rpx;
    }
    .card-subtitle {
      font-size: 28rpx;
    }
  }
  
  &.device-tablet {
    .card-title {
      font-size: 36rpx;
    }
    .card-subtitle {
      font-size: 32rpx;
    }
  }
  
  &.device-desktop {
    .card-title {
      font-size: 40rpx;
    }
    .card-subtitle {
      font-size: 36rpx;
    }
  }
}
</style>
